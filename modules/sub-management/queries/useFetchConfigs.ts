import { useQuery } from "@tanstack/react-query";

import { api } from "~/lib/api";

import { useSession } from "~/modules/login/auth-provider";

export const fetchClientConfigs = async (orgId: string) => {
  try {
    const response = await api
      .get<{ data: { legacy_app_modules: string[]; uni_id: string } }>(
        `client/info?uni_id=${orgId}`
      )
      .json();

    return {
      modules: response?.data?.legacy_app_modules || [],
      uniId: response.data.uni_id,
    };
  } catch (err) {
    throw new Error("Could not fetch configs");
  }
};

export const useFetchConfigs = () => {
  const { data: sessionData } = useSession();

  return useQuery({
    queryKey: [sessionData?.university_id, "FETCH_CONFIGS"],
    queryFn: () => fetchClientConfigs(sessionData?.university_id as string),
    enabled: Boolean(sessionData?.university_id),
  });
};
