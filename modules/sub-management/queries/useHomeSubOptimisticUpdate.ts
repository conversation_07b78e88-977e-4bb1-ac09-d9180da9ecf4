import { useQueryClient } from "@tanstack/react-query";
import { useSession } from "~/modules/login/auth-provider";

/**
 * Hook for optimistically updating sub request data in the home tab
 */
export function useHomeSubOptimisticUpdate() {
  const { data: sessionData } = useSession();
  const queryClient = useQueryClient();

  /**
   * Force refresh of sub request data
   */
  const refreshSubRequests = () => {
    // Invalidate all sub request queries
    queryClient.invalidateQueries({
      queryKey: [sessionData?.university_id],
    });
  };

  return {
    refreshSubRequests
  };
}
