import { formatClassDate } from "./../../classes/utils";
import { useQuery } from "@tanstack/react-query";
import { SubRequestType } from "~/components/modules/sub/types";

import { api } from "~/lib/api";

import { useSession } from "~/modules/login/auth-provider";

export const fetchSubs = async (): Promise<SubRequestType> => {
  try {
    const urlParams = new URLSearchParams({
      from_date: formatClassDate(),
      status: "ALL",
    }).toString();

    const response = await api
      .get<{ data: SubRequestType }>(
        `classes/sub_management/instructor_updates?${urlParams}`
      )
      .json();
    return response.data || [];
  } catch (err) {
    throw new Error("Could not fetch subs");
  }
};

export const useFetchSubs = () => {
  const { data: sessionData } = useSession();

  return useQuery({
    queryKey: [sessionData?.university_id],
    queryFn: async () => await fetchSubs(),
    enabled: Bo<PERSON><PERSON>(sessionData?.university_id),
  });
};
