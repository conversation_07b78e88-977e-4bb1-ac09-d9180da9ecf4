import { formatClassDate, formatTime } from "./../../classes/utils";
import { useQuery } from "@tanstack/react-query";

import { map } from "lodash/fp";

import { api } from "~/lib/api";
import { ClassDetailsResponse } from "~/modules/classes/types";

import { useSession } from "~/modules/login/auth-provider";

export const fetchClasses = async (date?: string) => {
  try {
    const urlParams = new URLSearchParams({
      date: date as string,
    }).toString();

    const response = await api
      .get<{ classes: ClassDetailsResponse[] }>(`classes/list?${urlParams}`)
      .json();
    return response.classes || [];
  } catch (err) {
    throw new Error("Could not fetch configs");
  }
};

type Options = {
  date?: Date;
};

export const useFetchClasses = (option?: Options) => {
  const { data: sessionData } = useSession();

  const formattedDate = formatClassDate(option?.date);

  return useQuery({
    queryKey: [sessionData?.university_id, formattedDate],
    queryFn: () => fetchClasses(formattedDate),
    select: (data) =>
      map(
        (val) => ({
          id: String(val?.id),
          title: `${val?.name}, ${val.gym_name.trim()} • ${formatTime(
            val?.start_time
          )} - ${formatTime(val?.end_time)}`,
        }),
        data
      ),
    enabled: Boolean(formattedDate),
  });
};
