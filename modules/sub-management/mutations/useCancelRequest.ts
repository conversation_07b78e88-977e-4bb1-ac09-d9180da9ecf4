import { useMutation, useQueryClient } from "@tanstack/react-query";

import { useSession } from "~/modules/login/auth-provider";

import { showErrorToast, showSuccessToast } from "~/components/toast";
import { api } from "~/lib/api";

export const cancelRequest = async (id: number) => {
  try {
    return await api.delete(`classes/sub_management/delete_sub/${id}`).json();
  } catch (error) {
    // @ts-expect-error
    if (error?.name === "HTTPError") {
      // @ts-expect-error
      return await error?.response?.json();
    }
  }
};

export const useCancelRequest = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  const { data: sessionData } = useSession();

  return useMutation({
    mutationFn: cancelRequest,
    onSuccess: async (data) => {
      if (data.success) {
        onSuccess?.();
        await queryClient.invalidateQueries({
          queryKey: [sessionData?.university_id],
        });
        return showSuccessToast(data.message);
      }
    },
    onSettled: (val) => {
      if (!val.success) showErrorToast(val.message);
    },
  });
};
