import { useMutation, useQueryClient } from "@tanstack/react-query";

import { getSession, useSession } from "~/modules/login/auth-provider";

import { showErrorToast, showSuccessToast } from "~/components/toast";
import { RequestSubSchema } from "../request-sub-form";

import * as z from "zod";
import { formatClassDate } from "~/modules/classes/utils";
import { api } from "~/lib/api";

const transformValues = (
  formData: z.infer<typeof RequestSubSchema>,
  instructorId: string
) => {
  const { classes, date, reason, message } = formData;

  return {
    class_ids: classes.map((cls) => cls.id),
    date: formatClassDate(date),
    instructor_id: instructorId,
    notes: message,
    notify_of_sub_request: false,
    reason: reason.title,
    sub_request_type: "class",
  };
};

export const createSubRequest = async (
  data: z.infer<typeof RequestSubSchema>
) => {
  const session = await getSession();

  try {
    return await api
      .post<{ message: string; success: boolean }>(
        "classes/sub_management/request_sub",
        {
          json: transformValues(data, session?.id ?? ""),
        }
      )
      .json();
  } catch (error) {
    // @ts-expect-error
    if (error?.name === "HTTPError") {
      // @ts-expect-error
      return await error?.response?.json();
    }
  }
};

export const useCreateSubRequest = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  const { data: sessionData } = useSession();

  return useMutation({
    mutationFn: createSubRequest,
    onSuccess: async (data) => {
      if (data.success) {
        onSuccess?.();
        await queryClient.invalidateQueries({
          queryKey: [sessionData?.university_id],
        });
        return showSuccessToast(data.message);
      }
    },
  });
};
