import { useMutation, useQueryClient } from "@tanstack/react-query";

import { useSession } from "~/modules/login/auth-provider";

import { showErrorToast, showSuccessToast } from "~/components/toast";
import { api } from "~/lib/api";

export const createICanSub = async (id: number) => {
  try {
    return await api
      .post<{ message: string; success: boolean }>(
        "classes/sub_management/i_can_sub",
        {
          json: {
            sub_request_id: id,
          },
        }
      )
      .json();
  } catch (error) {
    // @ts-expect-error
    if (error?.name === "HTTPError") {
      // @ts-expect-error
      return await error?.response?.json();
    }
  }
};

export const useICanSub = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  const { data: sessionData } = useSession();

  return useMutation({
    mutationFn: createICanSub,
    onSuccess: async (data) => {
      if (data.success) {
        onSuccess?.();
        await queryClient.invalidateQueries({
          queryKey: [sessionData?.university_id],
        });
        return showSuccessToast(data.message);
      }
    },
    onSettled: (val) => {
      if (!val?.success) showErrorToast(val?.message as string);
    },
  });
};
