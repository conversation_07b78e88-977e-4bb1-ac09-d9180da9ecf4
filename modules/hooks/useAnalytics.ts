import { useCallback, useEffect } from "react";
import {
  EVENTS,
  logEvent,
  logScreenView,
  setUserId,
  setUserProperties,
} from "~/lib/firebase";
import { useSession } from "../login/auth-provider";

export const useAnalytics = () => {
  const { data: sessionData } = useSession();

  // Set user ID and properties when session changes
  useEffect(() => {
    if (sessionData) {
      // Set user ID for analytics
      setUserId(sessionData.id?.toString() || null);

      // Set user properties
      const userProperties: Record<string, string> = {
        email: sessionData.email || "",
        university_id: sessionData.university_id?.toString() || "",
        user_type: "instructor",
      };

      if (sessionData.first_name && sessionData.last_name) {
        userProperties.name = `${sessionData.first_name} ${sessionData.last_name}`;
      }

      setUserProperties(userProperties);
    } else {
      // Clear user ID when logged out
      setUserId(null);
    }
  }, [sessionData]);

  // Track screen view
  const trackScreenView = useCallback(
    (screenName: string, screenClass?: string) => {
      logScreenView(screenName, screenClass);
    },
    []
  );

  // Track event
  const trackEvent = useCallback(
    (eventName: string, params?: Record<string, unknown>) => {
      logEvent(eventName, params);
    },
    []
  );

  return {
    trackScreenView,
    trackEvent,
    EVENTS,
  };
};
