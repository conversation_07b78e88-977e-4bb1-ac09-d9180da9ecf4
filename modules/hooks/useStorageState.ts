import { useEffect, useCallback, useReducer } from "react";
import * as SecureStore from "expo-secure-store";

type UseStateHook<T> = [
  { loading: boolean; value: T | undefined },
  (value: T | undefined) => void
];

function useAsyncState<T>(
  initialValue: { loading: boolean; value: T | undefined } = {
    loading: true,
    value: undefined,
  }
): UseStateHook<T> {
  return useReducer(
    (
      state: { loading: boolean; value: T | undefined },
      action: T | undefined = undefined
    ): { loading: boolean; value: T | undefined } => ({
      loading: false,
      value: action,
    }),
    initialValue
  ) as UseStateHook<T>;
}

export async function setStorageItemAsync(
  key: string,
  value: Record<string, string> | null
) {
  if (value == undefined) {
    await SecureStore.deleteItemAsync(key);
  } else {
    await SecureStore.setItemAsync(key, JSON.stringify(value));
  }
}

export function useStorageState(
  key: string
): UseStateHook<Record<string, string>> {
  const [state, setState] = useAsyncState<Record<string, string> | undefined>();

  useEffect(() => {
    try {
      SecureStore.getItemAsync(key).then((value) => {
        if (value) {
          setState(JSON.parse(value));
        } else {
          setState(undefined);
        }
      });
    } catch (error) {
      throw new Error("Sorry, an error occurred while retrieving the data.");
    }
  }, [key]);

  const setValue = useCallback(
    (value: Record<string, string> | undefined) => {
      setState(value);
      setStorageItemAsync(key, value as unknown as Record<string, string>);
    },
    [key]
  );

  return [state, setValue];
}
