// import { useSession } from "../login/auth-provider";
import { useFetchConfigs } from "../sub-management/queries/useFetchConfigs";

export enum FEATURES {
  SUB_MANAGER_MODULE = "sub_manager",
}

// const UPACE_EMAILS = [
//   "@upace.com",
//   "@upace.dev",
//   "@upace.app",
//   "@upaceapp.com",
// ];

export const useFeatureEnabled = (feature?: FEATURES) => {
  // const { data: sessionData } = useSession();

  const { data } = useFetchConfigs();

  const featureEnabled =
    data && feature ? data?.modules?.includes?.(feature) : false;

  // const isUpaceUser = UPACE_EMAILS.some((email) =>
  //   sessionData?.email?.includes(email)
  // );

  return featureEnabled;
};
