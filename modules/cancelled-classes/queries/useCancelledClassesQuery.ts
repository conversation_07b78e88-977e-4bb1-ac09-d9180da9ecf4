import { useQuery } from "@tanstack/react-query";
import { api } from "~/lib/api";
import { ClassCancellation } from "../types";

import { formatClassDate } from "~/modules/classes/utils";

export const fetchCancelledClasses = async ({
  params,
}: {
  params?: Record<string, string>;
}): Promise<ClassCancellation[]> => {
  try {
    const urlParams = new URLSearchParams({
      ...params,
    }).toString();

    const response = await api
      .get<{ data: ClassCancellation[] }>(
        `classes/cancellation/history?${urlParams}`
      )
      .json();

    return response?.data || [];
  } catch (err) {
    throw new Error("Could not fetch reservations");
  }
};

export const useCancelledClassesQuery = (date?: Date) => {
  const formattedDate = formatClassDate(date);

  return useQuery({
    queryKey: ["cancelled-classes", formattedDate],
    queryFn: async () =>
      fetchCancelledClasses({ params: { date: formattedDate } }),
  });
};
