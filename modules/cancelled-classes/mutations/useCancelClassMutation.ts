import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "~/lib/api";
import { showSuccessToast } from "~/components/toast";
import { formatClassDate } from "~/modules/classes/utils";

export const cancelClass = async ({
  date,
  reason,
  slot_ids,
  cancellation_id,
}: {
  date: Date;
  reason: string;
  slot_ids: number[];
  cancellation_id?: string;
}) => {
  try {
    const response = await api
      .post<{ message: string; success: boolean }>(
        "classes/cancellation/save",
        {
          json: {
            reason,
            slot_ids,
            date: formatClassDate(date),
            cancellation_id,
            hide_in_app: false,
          },
        }
      )
      .json();
    return response;
  } catch (error) {
    return {
      success: false,
      message: "Unable to cancel classes",
    };
  }
};

export const useCancelClassMutation = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: cancelClass,
    onSuccess: async (data) => {
      if (data.success) {
        await queryClient.invalidateQueries({
          queryKey: ["cancelled-classes"],
        });

        showSuccessToast(data.message);
        onSuccess?.();
      }
    },
  });
};
