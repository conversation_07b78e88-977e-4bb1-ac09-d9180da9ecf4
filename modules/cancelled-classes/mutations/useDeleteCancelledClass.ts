import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "~/lib/api";
import { showSuccessToast } from "~/components/toast";

export const deleteCancelClass = async (id: number) => {
  try {
    const response = await api
      .delete<{ message: string; success: boolean }>(
        `classes/cancellation/${id}/delete`
      )
      .json();
    return response;
  } catch (error) {
    return {
      success: false,
      message: "Unable to cancel classes",
    };
  }
};

export const useDeleteCancelledClass = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteCancelClass,
    onSuccess: async (data) => {
      if (data.success) {
        await queryClient.invalidateQueries({
          queryKey: ["cancelled-classes"],
        });

        showSuccessToast(data.message);
        onSuccess?.();
      }
    },
  });
};
