/**
 * Interface for appointment data used in calendar integration
 */
export interface CalendarAppointment {
  id: string | number;
  title: string;
  startTime: string;
  endTime: string;
  location?: string;
  notes?: string;
  type?: string;
}

/**
 * Interface for successful appointment data from API response
 */
export interface SuccessfulAppointment {
  res_id: string | number;
  date: Date;
  equipment: {
    id: string | number;
    title?: string;
  };
  user: {
    id: string | number;
    title?: string;
  };
  startTime: {
    id: string;
    title: string;
  };
  membership?: {
    id?: string | number;
  } | null;
  equipmentType?: {
    id?: string | number;
  } | null;
}

/**
 * Interface for API response
 */
export interface ApiResponse {
  success: boolean;
  message?: string;
  res_id?: string | number;
  details?: string;
  error?: string | Record<string, unknown>;
}

/**
 * Interface for HTTP error response
 */
export interface HttpErrorResponse {
  response?: {
    json: () => Promise<ApiResponse>;
  };
  message?: string;
  error?: string | Record<string, unknown>;
}
