import { addDays, format, isAfter, isSameDay } from "date-fns";
import { z } from "zod";
import { AppointmentSchema } from "~/components/modules/appointments/appointment-form";

type RecurringDays = {
  monday: boolean;
  tuesday: boolean;
  wednesday: boolean;
  thursday: boolean;
  friday: boolean;
  saturday: boolean;
  sunday: boolean;
};

const dayMap = {
  0: "sunday",
  1: "monday",
  2: "tuesday",
  3: "wednesday",
  4: "thursday",
  5: "friday",
  6: "saturday",
};

/**
 * Generates an array of dates for recurring appointments
 * @param startDate The start date of the recurring appointments
 * @param endDate The end date of the recurring appointments
 * @param recurringDays The days of the week to create appointments for
 * @returns An array of dates for the recurring appointments
 */
export const generateRecurringDates = (
  startDate: Date,
  endDate: Date,
  recurringDays: RecurringDays
): Date[] => {
  const dates: Date[] = [];
  let currentDate = new Date(startDate);

  // Skip the start date itself as it's already included in the form
  currentDate = addDays(currentDate, 1);

  while (!isAfter(currentDate, endDate) || isSameDay(currentDate, endDate)) {
    const dayOfWeek = currentDate.getDay();
    const dayName = dayMap[dayOfWeek as keyof typeof dayMap];

    if (recurringDays[dayName as keyof RecurringDays]) {
      dates.push(new Date(currentDate));
    }

    currentDate = addDays(currentDate, 1);
  }

  return dates;
};

/**
 * Generates an array of appointment data for recurring appointments
 * @param formData The form data from the appointment form
 * @returns An array of appointment data for the recurring appointments
 */
export const generateRecurringAppointments = (
  formData: z.infer<typeof AppointmentSchema>
): z.infer<typeof AppointmentSchema>[] => {
  if (
    !formData.recurring ||
    !formData.recurringEndDate ||
    !formData.recurringDays
  ) {
    return [formData];
  }

  const recurringDates = generateRecurringDates(
    formData.date,
    formData.recurringEndDate,
    formData.recurringDays
  );

  // Create a new appointment for each recurring date
  return recurringDates.map((date) => ({
    ...formData,
    date,
    recurring: false, // Set to false for the individual appointments
    recurringDays: undefined, // Remove recurring options from individual appointments
    recurringEndDate: undefined, // Remove recurring options from individual appointments
  }));
};
