import { useQuery } from "@tanstack/react-query";

import { sortBy } from "lodash/fp";

import { formatClassDate } from "~/modules/classes/utils";
import { Appointment } from "../types";
import { addMonths, startOfMonth, endOfMonth } from "date-fns";

import { api } from "~/lib/api";

export const fetchAppointments = async ({
  signal,
  selectedDate,
}: {
  signal?: AbortSignal;
  selectedDate?: Date;
}): Promise<Appointment[]> => {
  try {
    const startDate = selectedDate ? startOfMonth(selectedDate) : new Date();
    const endDate = selectedDate
      ? endOfMonth(selectedDate)
      : addMonths(new Date(), 5);

    const urlParams = new URLSearchParams({
      start_time: formatClassDate(startDate),
      end_time: formatClassDate(endDate),
    }).toString();

    const response = await api
      .get<{ data: { reservations: Appointment[] } }>(
        `reservations/mine?${urlParams}&type=pt`,
        { signal }
      )
      .json();

    return response?.data?.reservations || [];
  } catch (err) {
    throw new Error("Could not fetch appointments");
  }
};

export const useAppointments = (selectedDate?: Date) => {
  const date = selectedDate ? startOfMonth(selectedDate) : "";

  return useQuery({
    queryKey: ["appointments", date],
    queryFn: async ({ signal }) => fetchAppointments({ signal, selectedDate }),
    select: (data) => sortBy(["start_time", "equipment_names"], data),
  });
};
