import { useQueryClient } from "@tanstack/react-query";
import { useSession } from "~/modules/login/auth-provider";
import { Appointment } from "../types";

/**
 * Hook for optimistically updating appointment data in the home tab
 */
export function useHomeOptimisticUpdate() {
  const { data: sessionData } = useSession();
  const queryClient = useQueryClient();

  /**
   * Update appointment check-in status optimistically
   */
  const updateAppointmentCheckIn = (
    appointmentId: number,
    isCheckedIn: boolean
  ) => {
    // Optimistically update the appointment in the cache
    queryClient.setQueryData(
      ["appointments"],
      (oldData: Appointment[] | undefined) => {
        if (!oldData) return oldData;

        return oldData.map((appointment) => {
          if (appointment.id === appointmentId) {
            return {
              ...appointment,
              checkin: isCheckedIn ? 1 : 0,
            };
          }
          return appointment;
        });
      }
    );

    // Invalidate all appointment queries to ensure data is refreshed
    // This is done after the optimistic update to ensure the UI is updated immediately
    // while the background refresh happens
    setTimeout(() => {
      queryClient.invalidateQueries({
        queryKey: ["appointments"],
      });
    }, 500);
  };

  return {
    updateAppointmentCheckIn,
  };
}
