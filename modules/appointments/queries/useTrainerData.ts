import { useQuery } from "@tanstack/react-query";

import { formatTime, obtainDateFrame } from "~/modules/classes/utils";

import { api } from "~/lib/api";
import { useSession } from "~/modules/login/auth-provider";
import { TimeBlock, TrainingSession } from "~/modules/classes/types";

export const fetchTrainerData = async ({
  params,
  userId,
}: {
  userId: string;
  params?: Record<string, string | number>;
}) => {
  try {
    const urlParams = new URLSearchParams({
      date: params?.date as string,
      equipment_id: params?.equipment_id as string,
    }).toString();

    const response = await api
      .get<{
        data: {
          availableTimeBlocks: { gym_name: string; time_blocks: TimeBlock[] }[];
          memberships: unknown[];
          trainingSessions: TrainingSession[];
        };
      }>(`users/trainer/${userId}?${urlParams}&type=pt`)
      .json();

    return response?.data;
  } catch (err) {
    throw new Error("Could not fetch classes");
  }
};

export const useTrainerData = (searchParams?: Record<string, string>) => {
  const { data: sessionData } = useSession();

  return useQuery({
    queryKey: ["trainer", searchParams],
    queryFn: async () =>
      fetchTrainerData({
        params: searchParams,
        userId: sessionData?.id as string,
      }),

    select: (data) => ({
      ...data,
      trainingSessions: data.trainingSessions.map((item: TrainingSession) => ({
        id: String(item.id),
        title: item.name,
      })),
      availableTimeBlocks: data.availableTimeBlocks?.[0]?.time_blocks
        ?.filter((time) => !time.disabled)
        ?.map((time) => ({
          id: time.start,
          title: formatTime(time.start),
        })),
    }),
  });
};
