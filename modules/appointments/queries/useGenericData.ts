import { useQuery } from "@tanstack/react-query";
import { isEmpty } from "lodash/fp";

import { api } from "~/lib/api";

export const fetchGenericData = async (data: Record<string, unknown>) => {
  try {
    const response = await api
      .post<
        {
          gym_name: string;
          name: string;
          id: number;
        }[]
      >(`generics`, {
        json: {
          data: btoa(JSON.stringify(data)), // Convert it to base64
        },
      })
      .json();

    return response || [];
  } catch (err) {
    throw new Error("Could not fetch data");
  }
};

const equipmentQuery = {
  selectFields: ["equipment.id", "equipment.name", "gyms.name as gym_name"],
  entity: "equipment",
  university_id_join_entity: "equipment",
  joins: [
    {
      table: "gyms",
      glue: {
        leftComparator: "gyms.id",
        rightComparator: "equipment.gym_id",
      },
    },
  ],
  where: [
    {
      selector: "equipment.deleted",
      op: "=",
      value: "0",
    },
    {
      selector: "equipment.active",
      op: "=",
      value: "1",
    },
    {
      selector: "equipment.type_id",
      op: "!=",
      value: 10,
    },
    {
      selector: "equipment.type_id",
      op: "!=",
      value: 9,
    },
  ],
  orderBy: [
    {
      field: "equipment.name",
      direction: "asc",
    },
  ],
};

export const useGenericData = (query?: Record<string, unknown>) => {
  const defaultQuery = query ?? equipmentQuery;

  return useQuery({
    queryKey: ["generic", defaultQuery],
    queryFn: async () => fetchGenericData(defaultQuery),
    select: (rec) =>
      rec.map((item) => ({
        id: String(item.id),
        title: `${item.name} - ${item.gym_name}`,
      })),
    enabled: !isEmpty(defaultQuery),
  });
};
