import { useQuery } from "@tanstack/react-query";

import { sortBy } from "lodash/fp";

import { startOfYear, endOfYear } from "date-fns";

import { api } from "~/lib/api";
import { Reservation } from "~/modules/classes/types";

export const fetchReservations = async ({
  signal,
  params,
}: {
  signal?: AbortSignal;
  params?: Record<string, string>;
}): Promise<Reservation[]> => {
  try {
    const urlParams = new URLSearchParams({
      ...params,
    }).toString();

    const response = await api
      .get<{ data: { reservations: Reservation[] } }>(
        `reservations/overview?${urlParams}`,
        { signal }
      )
      .json();

    return response?.data?.reservations || [];
  } catch (err) {
    throw new Error("Could not fetch reservations");
  }
};

export const useReservations = (queryParams?: Record<string, string>) => {
  const defaultParams = {
    start_time: startOfYear(new Date()).toISOString(),
    end_time: endOfYear(new Date()).toISOString(),
    ...queryParams,
  };

  return useQuery({
    queryKey: ["reservations", defaultParams],
    queryFn: async ({ signal }) =>
      fetchReservations({ signal, params: defaultParams }),
    select: (data) => sortBy(["start_time", "equipment_names"], data),
  });
};
