export interface Appointment {
  id: number;
  first_name: string;
  last_name: string;
  start_time: string;
  end_time: string;
  room_name: string;
  equipment_name: string;
  checkin: number;
  image: string | null;
  active?: number;
  added_by?: number;
  added_by_first_name?: string;
  added_by_last_name?: string;
  attending_persons?: number;
  cancellation_source?: null;
  cancellation_user?: null;
  cancelled_on?: null;
  checked_in_at?: null;
  child_age?: null;
  child_name?: null;
  class_id?: number;
  class_name?: null;
  classes_id?: null;
  client_name?: string;
  created?: string;
  decrements_sync_id?: null;
  deleted?: number;
  email?: string;
  equ_id?: null;
  equipment_id?: number;
  equipment_type_id?: number;
  gym_id?: number;
  gym_name?: string;
  has_feedback?: number;
  instructor_first_name?: string;
  instructor_last_name?: string;
  is_member?: number;
  is_no_show?: number;
  is_virtual?: number;
  membership_id?: string | null;
  membership_package?: null;
  membership_purchase_id?: null;
  meta?: string;
  notes?: null;
  paid_session_id?: null;
  ref_type?: number;
  room_id?: number;
  schedule_id?: null;
  source?: string;
  trainer_id?: number;
  trainer_pay_amount?: null;
  updated?: string;
  user_id?: number;
  pt_equipment_name?: string;
}
