import { createContext, useContext } from "react";
import { LoginUserResponse, useLoginQuery } from "./useLoginQuery";
import { router } from "expo-router";
import { useStorageState } from "../hooks/useStorageState";
import { EVENTS, logEvent } from "~/lib/firebase";

import * as SecureStore from "expo-secure-store";

export type UserSessionType = {
  signIn: ({
    email,
    password,
    orgId,
  }: {
    email: string;
    password: string;
    orgId?: string;
  }) => void;
  data?: LoginUserResponse | undefined;
  signOut: () => void;
  isError?: boolean;
  isLoading?: boolean;
  isStarting?: boolean;
  error?: Error | null;
};

const CurrentUserContext = createContext<UserSessionType>({
  signIn: () => {},
  signOut: () => {},
});

export const UPACE_TOKEN = "__upace";

export const useSession = () => useContext(CurrentUserContext);

export const getSession = async () => {
  const record = await SecureStore.getItemAsync(UPACE_TOKEN);

  if (record) {
    const session: UserSessionType["data"] = JSON.parse(record);
    return session;
  }

  return undefined;
};

export const signOut = async () =>
  await SecureStore.deleteItemAsync(UPACE_TOKEN);

export const AuthContextProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [session, setSession] = useStorageState(UPACE_TOKEN);

  const {
    mutateAsync: signIn,
    isError,
    isPending,
    error,
  } = useLoginQuery((rec) => {
    if (rec) {
      setSession({
        ...rec,
        name: `${rec?.first_name} ${rec?.last_name}`,
      });

      // Track login event
      logEvent(EVENTS.LOGIN, {
        user_id: rec.id,
        email: rec.email,
        university_id: rec.university_id,
      });

      router.replace("/(classes)/(tabs)");
    }
  });

  return (
    <CurrentUserContext.Provider
      value={{
        signIn,
        data: session?.value as LoginUserResponse | undefined,
        isError,
        error,
        signOut: async () => {
          // Track logout event before clearing the session
          if (session?.value) {
            const userData = session.value as unknown as LoginUserResponse;
            logEvent(EVENTS.LOGOUT, {
              user_id: userData.id,
              email: userData.email,
            });
          }

          await setSession(undefined);
          router.replace("/(auth)/sign-in");
        },
        isLoading: isPending,
        isStarting: session.loading,
      }}
    >
      {children}
    </CurrentUserContext.Provider>
  );
};
