import { zodResolver } from "@hookform/resolvers/zod";
import * as React from "react";
import { useForm } from "react-hook-form";
import { View } from "react-native";
import * as z from "zod";
import { Button } from "~/components/ui/button";
import { Form, FormField, FormInput } from "~/components/ui/form";

import { Text } from "~/components/ui/text";
import { router } from "expo-router";

const formSchema = z.object({
  email: z.string().trim().email({
    message: "Email address is required",
  }),
  password: z.string().trim().min(3, {
    message: "Password is required",
  }),
});

export const LoginForm = ({
  isLoading,
  isError,
  handleLogin,
  errorMessage,
}: {
  isLoading?: boolean;
  isError?: boolean;
  errorMessage?: string;
  handleLogin: ({
    email,
    password,
  }: {
    email: string;
    password: string;
  }) => void;
}) => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    return handleLogin(values);
  }

  return (
    <Form {...form}>
      <View>
        {isError && (
          <Text style={{ color: "red", marginBottom: 5 }}>
            {errorMessage || " Email and Password wrong, try again"}
          </Text>
        )}
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormInput
              textContentType="oneTimeCode"
              placeholder="Email"
              autoCapitalize="none"
              autoComplete="email"
              {...field}
            />
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormInput
              textContentType="oneTimeCode"
              placeholder="Password"
              secureTextEntry
              autoComplete="password"
              {...field}
            />
          )}
        />

        <View className="flex flex-col justify-between  mt-2">
          <Button
            disabled={isLoading}
            onPress={form.handleSubmit(onSubmit)}
            style={{ backgroundColor: "#E93B92" }}
            label="Login"
            isLoading={isLoading}
            textClassName="text-white"
          />
          <Button
            variant={"link"}
            className="bg-transparent"
            onPress={() => router.push("/(auth)/forgot-password")}
            label="Forgot Password?"
            textClassName="text-black underline"
          />
        </View>
      </View>
    </Form>
  );
};
