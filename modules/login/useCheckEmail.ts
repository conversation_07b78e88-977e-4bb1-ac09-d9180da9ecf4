import { useMutation } from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "~/components/toast";
import { BASE_API_URL_CLIENT } from "~/constants/base-url";

export const checkEmail = async (email: string) => {
  const headers: { "Content-Type": string; authorization?: string } = {
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(
      `${BASE_API_URL_CLIENT}auth/request_password_reset`,
      {
        headers,
        method: "POST",
        body: JSON.stringify({ email }),
      }
    );

    return await response.json();
  } catch (err) {
    throw new Error("Sorry there is a problem resetting the password");
  }
};

export const useCheckEmail = (onSuccess?: (email: string) => void) => {
  return useMutation({
    mutationFn: checkEmail,

    onSuccess: async (data, requestData) => {
      if (data.success) {
        onSuccess?.(requestData);
        return showSuccessToast(data?.message);
      }
      return showErrorToast(data?.message);
    },

    onError: (e) => {
      return showErrorToast(e?.message);
    },
  });
};
