import { useMutation } from "@tanstack/react-query";
import { BASE_API_URL_CLIENT } from "~/constants/base-url";

export interface LoginUserResponse {
  success: string;
  message: string;
  token: string;
  university_id: string;
  first_name?: string;
  last_name?: string;
  name?: string;
  id: string;
  email?: string;
  primary_role?: string;
  refresh_token: string;
}

export const login = async ({
  email,
  password,
}: {
  email: string;
  password: string;
  orgId?: string;
}): Promise<LoginUserResponse> => {
  try {
    const response = await fetch(`${BASE_API_URL_CLIENT}auth/login/legacy`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email, password }),
    });

    if (response.ok) {
      return await response.json();
    }

    return Promise.reject(new Error("Authentication failed"));
  } catch (error) {
    throw new Error("Unable to login user: " + error); // Updated to include the original error message
  }
};

enum Roles {
  INSTRUCTOR = "6",
}

export const useLoginQuery = (onSuccess?: (data?: LoginUserResponse) => void) =>
  useMutation({
    mutationFn: login,
    onSuccess: (data) => {
      if (String(data.primary_role) !== Roles.INSTRUCTOR) {
        throw new Error(
          "This app is for instructors and trainers, to access the app please update your permission levels"
        );
      }
      return onSuccess?.(data);
    },
  });
