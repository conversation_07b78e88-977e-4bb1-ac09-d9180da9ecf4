import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import * as React from "react";
import { useForm } from "react-hook-form";
import { View } from "react-native";
import * as z from "zod";
import { Button } from "~/components/ui/button";
import { Form, FormField, FormInput } from "~/components/ui/form";

import { Text } from "~/components/ui/text";
import { Loader } from "~/components/modules/classes/loader";
import { router } from "expo-router";
import { useCheckEmail } from "./useCheckEmail";

export const ResetPasswordSchema = z.object({
  email: z.string().trim().min(2, {
    message: "Email is required",
  }),
});

export const ForgotForm = ({}: {}) => {
  const form = useForm<z.infer<typeof ResetPasswordSchema>>({
    resolver: zodResolver(ResetPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  const {
    mutate: checkEmail,
    data: emailData,
    isPending,
  } = useCheckEmail((requestEmail: string) =>
    router.push({
      pathname: "/(auth)/[email]",
      params: {
        email: requestEmail,
      },
    })
  );

  const onSubmit = (formData: z.infer<typeof ResetPasswordSchema>) => {
    return checkEmail(formData.email);
  };

  return (
    <Form {...form}>
      <View>
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormInput
              textContentType="oneTimeCode"
              disabled={Boolean(emailData?.success)}
              placeholder="Email"
              autoCapitalize="none"
              autoComplete="email"
              {...field}
            />
          )}
        />

        <View className="flex flex-col justify-between  mt-2">
          <Button
            disabled={isPending}
            onPress={form.handleSubmit(onSubmit)}
            style={{ backgroundColor: "#E93B92" }}
            label="Reset Password"
            isLoading={isPending}
          />

          <Button
            variant={"link"}
            onPress={() => router.push("/(auth)/sign-in")}
            label="Back to login"
            textClassName="text-black underline"
            className="bg-transparent"
          />
        </View>
      </View>
    </Form>
  );
};
