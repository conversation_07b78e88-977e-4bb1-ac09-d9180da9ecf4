import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import * as React from "react";
import { useForm } from "react-hook-form";
import { View } from "react-native";
import * as z from "zod";
import { Button } from "~/components/ui/button";
import { Form, FormField, FormInput } from "~/components/ui/form";

import { router, useLocalSearchParams } from "expo-router";
import { useResetPassword } from "./useResetPassword";

export const ResetPasswordSchema = z
  .object({
    email: z.string().trim().min(2, {
      message: "Email is required",
    }),
    password: z.string().trim().min(1, {
      message: "Password is required",
    }),
    confirm_password: z.string().trim().min(1, {
      message: "Confirm password is required",
    }),
    pin: z.string().min(1, {
      message: "Pin is required",
    }),
  })
  .refine((data) => data.password === data.confirm_password, {
    message: "Passwords don't match",
    path: ["confirm_password"],
  });

export const ResetPassword = ({}: {}) => {
  const { email } = useLocalSearchParams<{ email: string }>();

  const form = useForm<z.infer<typeof ResetPasswordSchema>>({
    resolver: zodResolver(ResetPasswordSchema),
    defaultValues: {
      email: email,
      password: "",
      confirm_password: "",
    },
  });

  const { mutate: resetPassword, isPending: isLoading } = useResetPassword(() =>
    router.push("/(auth)/sign-in")
  );

  const onSubmit = (formData: z.infer<typeof ResetPasswordSchema>) => {
    return resetPassword(formData);
  };

  return (
    <Form {...form}>
      <View>
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormInput
              editable={!Boolean(email)}
              textContentType="oneTimeCode"
              disabled={Boolean(email)}
              placeholder="Email"
              autoCapitalize="none"
              autoComplete="email"
              {...field}
            />
          )}
        />

        <>
          <FormField
            control={form.control}
            name="pin"
            render={({ field }) => (
              <FormInput
                textContentType="oneTimeCode"
                placeholder="Pin"
                autoCapitalize="none"
                inputMode="numeric"
                {...field}
              />
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormInput
                textContentType="oneTimeCode"
                placeholder="Password"
                autoCapitalize="none"
                secureTextEntry
                {...field}
              />
            )}
          />

          <FormField
            control={form.control}
            name="confirm_password"
            render={({ field }) => (
              <FormInput
                textContentType="oneTimeCode"
                placeholder="Confirm password"
                autoCapitalize="none"
                secureTextEntry
                {...field}
              />
            )}
          />
        </>

        <View className="flex flex-col justify-between  mt-2">
          <Button
            disabled={isLoading}
            onPress={form.handleSubmit(onSubmit)}
            style={{ backgroundColor: "#E93B92" }}
            label="Reset Password"
            isLoading={isLoading}
          />

          <Button
            variant={"link"}
            onPress={() => router.push("/(auth)/sign-in")}
            label="Back to login"
            textClassName="text-black underline"
            className="bg-transparent"
          />
        </View>
      </View>
    </Form>
  );
};
