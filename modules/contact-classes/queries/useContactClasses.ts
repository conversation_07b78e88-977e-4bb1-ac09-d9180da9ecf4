import { useQuery } from "@tanstack/react-query";
import { api } from "~/lib/api";

import { formatClassDate } from "~/modules/classes/utils";
import { ContactClass } from "../types";

export const fetchContactClasses = async ({
  params,
}: {
  params?: Record<string, string>;
}): Promise<ContactClass[]> => {
  try {
    const urlParams = new URLSearchParams({
      ...params,
    }).toString();

    const response = await api
      .get<{ data: ContactClass[] }>(`classes/contact?${urlParams}`)
      .json();

    return response?.data || [];
  } catch (err) {
    throw new Error("Could not fetch contact classes");
  }
};

export const useContactClasses = (date?: Date) => {
  const formattedDate = formatClassDate(date);

  return useQuery({
    queryKey: ["contact-classes", formattedDate],
    queryFn: async () =>
      fetchContactClasses({ params: { date: formattedDate } }),
  });
};
