import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "~/lib/api";
import { showSuccessToast } from "~/components/toast";
import { formatClassDate } from "~/modules/classes/utils";

export const createContactClass = async (
  data: Record<string, Date | string | string[]>
) => {
  try {
    const response = await api
      .post<{ message: string; success: boolean }>("classes/contact", {
        json: {
          ...data,
          date: formatClassDate(data?.date as Date),
        },
      })
      .json();
    return response;
  } catch (error) {
    return {
      success: false,
      message: "Unable to cancel classes",
    };
  }
};

export const useCreateContactClass = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createContactClass,
    onSuccess: async (data) => {
      if (data.success) {
        await queryClient.invalidateQueries({
          queryKey: ["contact-classes"],
        });

        showSuccessToast(data.message);
        onSuccess?.();
      }
    },
  });
};
