import { Text } from "~/components/ui/text";

import { useMemo } from "react";

import { useSearchUsers } from "~/modules/classes/mutations/useSearchUsers";

import {
  AutocompleteDropdown,
  AutocompleteDropdownItem,
} from "react-native-autocomplete-dropdown";

import { View } from "react-native";

export function SearchUsers({
  onSelect,
  position = "down",
}: {
  onSelect?: (item: AutocompleteDropdownItem | null) => void;
  position?: "up" | "down";
}) {
  const { mutate: searchUsers, isPending, data = [] } = useSearchUsers();

  const suggestions = useMemo(() => {
    // Check if data is an array before mapping
    if (Array.isArray(data)) {
      return data.map((item: Record<string, string>) => ({
        id: item?.id,
        title: `${item.first_name} ${item.last_name} (${item.email})`,
      }));
    }
    return [];
  }, [data]);

  // No need for color scheme since we're using white background for both modes

  return (
    <View className="z-50">
      <AutocompleteDropdown
        debounce={1000}
        dataSet={suggestions}
        textInputProps={{
          placeholder: "Start searching members...",
          placeholderTextColor: "black",
          style: {
            color: "black",
          },
        }}
        inputContainerStyle={{
          padding: 2,
          backgroundColor: "white",
        }}
        suggestionsListContainerStyle={{
          position: "relative",
          zIndex: 9999,
          backgroundColor: "white",
        }}
        suggestionsListTextStyle={{
          color: "black",
        }}
        direction={position}
        onSelectItem={onSelect}
        loading={isPending}
        onChangeText={searchUsers}
        containerStyle={{
          flex: 1,
          borderColor: "gray",
          borderWidth: 1,
          borderRadius: 5,
          // paddingTop: 5,
          position: "relative",
          zIndex: 9999,
        }}
        bottomOffset={10}
        EmptyResultComponent={
          <Text className="text-center text-black">
            No user found, type to search
          </Text>
        }
      />
    </View>
  );
}
