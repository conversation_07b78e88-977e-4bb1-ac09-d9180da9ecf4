import Toast from "react-native-root-toast";
import { useMutation } from "@tanstack/react-query";

import { formatClassDate } from "../utils";
import { showErrorToast, showSuccessToast } from "~/components/toast";
import { useOptimisticUpdate } from "../queries/useOptimisticUpdate";
import { api } from "~/lib/api";

export const barcodeCheckIn = async ({
  classId,
  userId,
  uniId,
}: {
  classId: number;
  userId: number;
  uniId: string;
}) => {
  try {
    const response = await api
      .post<{ message: string; success: boolean }>(
        "checkintool/reservations/checkin",
        {
          json: {
            class_id: Number(classId),
            uni_id: String(uniId),
            user_id: Number(userId),
            date: formatClassDate(),
          },
        }
      )
      .json();

    return response;
  } catch (error) {
    // @ts-expect-error
    if (error?.name === "HTTPError") {
      // @ts-expect-error
      return await error?.response?.json();
    }
  }
};

export const useBarcodeCheckIn = () => {
  const invalidateQueries = useOptimisticUpdate();

  return useMutation({
    mutationFn: barcodeCheckIn,
    onSuccess: async (data) => {
      if (data?.success) {
        await invalidateQueries();
        return showSuccessToast(data?.message, Toast.positions.BOTTOM);
      }
      return showErrorToast(data?.message, Toast.positions.BOTTOM);
    },
    onError: (error) => showErrorToast(error?.message, Toast.positions.BOTTOM),
  });
};
