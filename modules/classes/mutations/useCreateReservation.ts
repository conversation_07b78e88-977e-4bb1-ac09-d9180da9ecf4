import { useMutation } from "@tanstack/react-query";

import { useOptimisticUpdate } from "../queries/useOptimisticUpdate";
import { showSuccessToast } from "~/components/toast";
import { api } from "~/lib/api";

export const createReservation = async (
  data: Record<string, string | boolean | number>,
  hideError?: boolean
) => {
  try {
    const response = await api
      .post<{ message: string; success: boolean }>("reservations/create", {
        json: {
          ...data,
          source: "ADMIN_PORTAL",
        },
        // Don't throw errors for when in recurring mode
        throwHttpErrors: hideError ? false : true,
      })
      .json();

    return response;
  } catch (error: unknown) {
    return {
      success: false,
      message: (error as string) || "Failed to create reservation",
    };
  }
};

export const useCreateReservation = (
  onSuccess?: (data?: any) => void,
  hideToast?: boolean
) => {
  const invalidateQueries = useOptimisticUpdate();

  return useMutation({
    mutationFn: (data: Record<string, string | boolean | number>) =>
      createReservation(data, hideToast),
    onSuccess: async (data) => {
      if (data.success) {
        await invalidateQueries();
        onSuccess?.(data);
        hideToast ? "" : showSuccessToast("Reservation created");
        return;
      }
    },
  });
};
