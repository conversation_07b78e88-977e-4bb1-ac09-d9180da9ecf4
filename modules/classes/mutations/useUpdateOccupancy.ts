import { useMutation } from "@tanstack/react-query";
import { useOptimisticUpdate } from "../queries/useOptimisticUpdate";
import { formatClassDate } from "../utils";
import { showSuccessToast } from "~/components/toast";
import { api } from "~/lib/api";

export const updateOccupancy = async (
  data: Record<string, string | boolean | number>
) => {
  try {
    return await api
      .put<{ message: string; success: boolean }>("classes/occupancy/update", {
        json: {
          ...data,
          date: formatClassDate(data?.date as string),
        },
      })
      .json();
  } catch (error) {
    return {
      success: false,
      message: "Unable to update occupancy",
    };
  }
};

export const useUpdateOccupancy = (onSuccess?: () => void) => {
  const invalidateQueries = useOptimisticUpdate();

  return useMutation({
    mutationFn: updateOccupancy,
    onSuccess: async (data) => {
      if (data.success) {
        await invalidateQueries();
        await onSuccess?.();
        return showSuccessToast(data.message);
      }
    },
  });
};
