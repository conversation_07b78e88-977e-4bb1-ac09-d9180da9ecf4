import { useMutation } from "@tanstack/react-query";

import { useOptimisticUpdate } from "../queries/useOptimisticUpdate";
import { showSuccessToast } from "~/components/toast";
import { api } from "~/lib/api";

export const checkIn = async (
  data: Record<string, string | boolean | number>
) => {
  try {
    const response = await api
      .post<{ message: string; success: boolean }>(`reservations/checkin`, {
        json: {
          ...data,
          reservation_id: data.id,
          checkin: <PERSON><PERSON><PERSON>(data.isCheckIn) ? 1 : 0,
          reservation_type: data?.type ?? "class",
        },
      })
      .json();

    return await response;
  } catch (error) {
    return {
      success: false,
      message: "Unable to check in",
    };
  }
};

export const useCheckInMutation = (onSuccess?: () => void) => {
  const invalidateQueries = useOptimisticUpdate();

  return useMutation({
    mutationFn: checkIn,
    onSuccess: async (data) => {
      if (data?.success) {
        await invalidateQueries();
        onSuccess?.();
        return showSuccessToast(data.message);
      }
    },
  });
};
