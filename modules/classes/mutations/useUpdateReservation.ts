import { useMutation } from "@tanstack/react-query";
import { useOptimisticUpdate } from "../queries/useOptimisticUpdate";
import { showSuccessToast } from "~/components/toast";
import { api } from "~/lib/api";

export const updateReservation = async (
  data: Record<string, string | boolean | number>
) => {
  try {
    const response = await api
      .put<{ message: string; success: boolean }>(`reservations/update`, {
        json: {
          ...data,
        },
      })
      .json();

    return response;
  } catch (error) {
    return {
      success: false,
      message: "Unable to update reservation",
    };
  }
};

export const useUpdateReservation = (onSuccess?: () => void) => {
  const invalidateQueries = useOptimisticUpdate();

  return useMutation({
    mutationFn: updateReservation,
    onSuccess: async (data) => {
      if (data.success) {
        await invalidateQueries();
        onSuccess?.();
        return showSuccessToast(data.message);
      }
    },
  });
};
