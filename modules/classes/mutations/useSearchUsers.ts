import { useMutation } from "@tanstack/react-query";

import { api } from "~/lib/api";

export const searchUsers = async (searchTerm: string) => {
  try {
    const response = await api
      .post<{ data: [] }>(`users/search`, {
        json: {
          search: searchTerm,
          limit: 30,
        },
      })
      .json();

    return response.data || [];
  } catch (error) {
    return {
      success: false,
      message: "Unable to search",
    };
  }
};

export const useSearchUsers = () =>
  useMutation({
    mutationFn: searchUsers,
  });
