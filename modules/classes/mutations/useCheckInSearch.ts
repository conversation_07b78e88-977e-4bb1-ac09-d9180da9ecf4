import { useMutation } from "@tanstack/react-query";

import { api } from "~/lib/api";

export const checkInSearch = async ({
  barcode,
  uniId,
}: {
  barcode: string;
  uniId: string;
}) => {
  try {
    const response = await api
      .post<{ data: [] }>(`checkintool/search`, {
        json: {
          barcode_search: barcode,
          uni_id: uniId,
        },
      })
      .json();

    return response?.data || [];
  } catch (error) {
    return {
      success: false,
      message: "Unable to search",
    };
  }
};

export const useCheckInSearch = () =>
  useMutation({
    mutationFn: checkInSearch,
  });
