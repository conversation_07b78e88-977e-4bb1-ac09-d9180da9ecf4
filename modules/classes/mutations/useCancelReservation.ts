import { useMutation } from "@tanstack/react-query";
import { useOptimisticUpdate } from "../queries/useOptimisticUpdate";

import { showSuccessToast } from "~/components/toast";
import { api } from "~/lib/api";

export const cancelReservation = async (id: number) => {
  try {
    const response = await api
      .get<{ message: string; success: boolean }>(
        `reservations/cancel/${id}?type=class`
      )
      .json();
    return await response;
  } catch (error) {
    return {
      success: false,
      message: "Unable to cancel reservation",
    };
  }
};

export const useCancelReservation = (onSuccess?: () => void) => {
  const invalidateQueries = useOptimisticUpdate();

  return useMutation({
    mutationFn: cancelReservation,
    onSuccess: async (data) => {
      if (data.success) {
        await invalidateQueries();
        onSuccess?.();
        return showSuccessToast(data.message);
      }
    },
  });
};
