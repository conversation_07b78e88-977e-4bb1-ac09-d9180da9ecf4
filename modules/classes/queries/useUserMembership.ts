import { useQuery } from "@tanstack/react-query";
import { api } from "~/lib/api";

export const fetchClassMembership = async ({
  params,
  signal,
}: {
  params?: Record<string, string | number>;
  signal?: AbortSignal;
}): Promise<{ package_name: string; id: string; display_text: string }[]> => {
  try {
    const urlParams = new URLSearchParams({
      user_id: params?.userId as string,
      item_id: params?.itemId as string,
      item_type: params?.type as string,
    }).toString();

    const rec = await api
      .get<{
        memberships: {
          package_name: string;
          id: string;
          display_text: string;
        }[];
      }>(`memberships/user?${urlParams}&include_no_passes=false`, {
        signal,
      })
      .json();

    return rec?.memberships || [];
  } catch (err) {
    throw new Error("Could not fetch classes");
  }
};

export const useUserMembership = (
  searchParams?: Record<string, string | number>
) => {
  return useQuery({
    queryKey: [searchParams],
    queryFn: async ({ signal }) =>
      fetchClassMembership({ params: searchParams, signal }),
    enabled: Boolean(searchParams?.userId && searchParams?.itemId),
  });
};
