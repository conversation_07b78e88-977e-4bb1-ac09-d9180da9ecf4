import { useQuery } from "@tanstack/react-query";

import { sortBy } from "lodash/fp";

import { ClassDetailsResponse } from "../types";
import { useSession } from "~/modules/login/auth-provider";
import { formatClassDate } from "../utils";
import { api } from "~/lib/api";

export const fetchClassesByOrgId = async ({
  params,
  signal,
}: {
  params?: Record<string, string | number>;
  signal?: AbortSignal;
}): Promise<ClassDetailsResponse[]> => {
  try {
    const urlParams = new URLSearchParams({
      ...params,
      date: formatClassDate(params?.date as string),
    }).toString();

    const rec = await api.get<{
      classes: ClassDetailsResponse[];
      code: number;
    }>(`classes/reservations/list?${urlParams}`, {
      signal,
    });

    const data = await rec.json();

    return data?.classes || [];
  } catch (err) {
    throw new Error("Could not fetch classes");
  }
};

export const useClassesData = (
  searchParams?: Record<string, string | number>
) => {
  const { data: sessionData } = useSession();

  const date = formatClassDate();

  return useQuery({
    queryKey: [sessionData?.token, date],
    queryFn: async ({ signal }) =>
      fetchClassesByOrgId({ params: searchParams, signal }),
    select: (data) => sortBy(["start_time", "name"], data),
  });
};
