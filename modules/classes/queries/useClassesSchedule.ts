import { useQuery } from "@tanstack/react-query";

import { sortBy } from "lodash/fp";

import { formatClassDate } from "~/modules/classes/utils";
import { addMonths, startOfMonth, endOfMonth } from "date-fns";

import { api } from "~/lib/api";
import { ClassScheduleItem } from "../types";

export const fetchClassesSchedule = async ({
  signal,
  selectedDate,
}: {
  signal?: AbortSignal;
  selectedDate?: Date;
}): Promise<ClassScheduleItem[]> => {
  try {
    const startDate = selectedDate ? startOfMonth(selectedDate) : new Date();
    const endDate = selectedDate
      ? endOfMonth(selectedDate)
      : endOfMonth(new Date());

    const urlParams = new URLSearchParams({
      start_date: formatClassDate(startDate),
      end_date: formatClassDate(endDate),
    }).toString();

    const response = await api
      .get<{ classes: ClassScheduleItem[] }>(`classes/range?${urlParams}`, {
        signal,
      })
      .json();

    return response?.classes || [];
  } catch (err) {
    throw new Error("Could not fetch classes range");
  }
};

export const useClassesSchedule = (selectedDate?: Date) => {
  const date = selectedDate ? startOfMonth(selectedDate) : "";

  return useQuery({
    queryKey: ["classes-range", date],
    queryFn: async ({ signal }) =>
      fetchClassesSchedule({ signal, selectedDate }),
    select: (data) => sortBy(["date", "start_time"], data),
  });
};
