import { useQuery } from "@tanstack/react-query";

import {
  Celebrations,
  ClassDetailsResponse,
  Occupancy,
  Reservation,
} from "../types";

import { formatClassDate } from "../utils";
import { useLocalSearchParams } from "expo-router";
import { flatten, map, values } from "lodash/fp";
import { api } from "~/lib/api";

export const fetchClassesById = async ({
  params,
  signal,
}: {
  params: Record<string, string | number>;
  signal?: AbortSignal;
}): Promise<
  { class: ClassDetailsResponse } & { reservations: Reservation[] } & {
    occupancy: Occupancy;
    celebrations: Celebrations;
  }
> => {
  try {
    const urlParams = new URLSearchParams({
      ...params,
      date: formatClassDate(params?.date as string),
      type: "class",
    }).toString();

    const rec = await api
      .get<{
        data: { class: ClassDetailsResponse } & {
          reservations: Reservation[];
        } & {
          occupancy: Occupancy;
          celebrations: Celebrations;
        };
      }>(`reservations?${urlParams}&include_celebrations=true`, {
        signal,
      })
      .json();

    return rec?.data || {};
  } catch (err) {
    throw new Error("Could not fetch waitlist");
  }
};

export const useClassByIdQuery = () => {
  const { id, date: paramDate } = useLocalSearchParams<{
    id: string;
    date?: string;
  }>();

  const date = formatClassDate(paramDate);

  return useQuery({
    queryKey: [{ entity_id: id, date }],
    queryFn: async ({ signal }) =>
      fetchClassesById({
        params: {
          entity_id: id,
          date,
        },
        signal,
      }),
    select: (data) => ({
      ...data,
      celebrations: {
        ...data.celebrations,
        MILESTONES: map(
          (val) => ({
            ...val,
            name: `${val?.first_name} ${val?.last_name} (${val?.total_reservations_count_in_period}th Class)`,
          }),
          flatten(values(data?.celebrations?.MILESTONES))
        ),
      },
    }),
  });
};
