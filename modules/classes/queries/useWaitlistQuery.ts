import { useQuery } from "@tanstack/react-query";

import { ClassDetailsResponse } from "../types";

import { formatClassDate } from "../utils";
import { useLocalSearchParams } from "expo-router";
import { api } from "~/lib/api";

export const fetchWaitlistById = async ({
  params,
  signal,
}: {
  params: Record<string, string | number>;
  signal?: AbortSignal;
}): Promise<ClassDetailsResponse[]> => {
  try {
    const urlParams = new URLSearchParams({
      date: formatClassDate(params?.date as string),
      class_id: params?.classId as string,
    }).toString();

    const response = await api
      .get<{ data: { waitlists: ClassDetailsResponse[] } }>(
        `classes/waitlists?${urlParams}`,
        {
          signal,
        }
      )
      .json();

    return response?.data?.waitlists || [];
  } catch (err) {
    throw new Error("Could not fetch waitlist");
  }
};

export const useWaitlistQuery = () => {
  const { id, date } = useLocalSearchParams<{ id: string; date?: string }>();

  const formattedDate = formatClassDate(date);

  return useQuery({
    queryKey: ["waitlist", id, formattedDate],
    select: (data: ClassDetailsResponse[]) => {
      // Sort waitlist by position_index (ascending order)
      return (data || []).sort(
        (a: ClassDetailsResponse, b: ClassDetailsResponse) => {
          const positionA = a.position ?? Number.MAX_SAFE_INTEGER;
          const positionB = b.position ?? Number.MAX_SAFE_INTEGER;
          return positionA - positionB;
        }
      );
    },
    queryFn: async ({ signal }) =>
      fetchWaitlistById({
        params: {
          classId: id,
          date: formattedDate,
        },
        signal,
      }),
  });
};
