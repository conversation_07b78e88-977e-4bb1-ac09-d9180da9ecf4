import { format, parse, parseISO } from "date-fns";

export const DEFAULT_DATE_FORMAT = "yyyy-MM-dd";

export const formatClassDate = (date?: string | Date | undefined | null) => {
  if (date && typeof date === "object" && date instanceof Date) {
    return format(parseISO(date.toISOString()), DEFAULT_DATE_FORMAT);
  }

  const isoDate = date ? parseISO(date) : parseISO(new Date().toISOString());
  return format(isoDate, DEFAULT_DATE_FORMAT);
};

export const getInitials = (name: string) => {
  const words = name.split(" ");

  if (words?.length >= 2) {
    return (
      words[0].charAt(0) + words[words?.length - 1].charAt(0)
    ).toUpperCase();
  }

  return words[0].charAt(0).toUpperCase();
};

export const formatTime = (time: string) => {
  if (!time) {
    return "-";
  }

  const splittedTime = time.split(" ");

  const timeSplitted = splittedTime[1] ?? splittedTime[0] ?? "";

  const parsedTime = parse(timeSplitted, "HH:mm:ss", new Date());

  // To allow no space between time and am/pm
  return format(parsedTime, "h:mmaaa");
};

export const obtainDateFrame = (startTime: string, endTime: string) => {
  if (startTime?.length === 5) {
    startTime += ":00";
  }

  if (endTime?.length === 5) {
    endTime += ":00";
  }
  return `${formatTime(startTime)} - ${formatTime(endTime)}`;
};

export const formatHourMinsTime = (time: string) => {
  const parsedTime = parse(time, "HH:mm:ss", new Date());
  return format(parsedTime, "HH:MM");
};
