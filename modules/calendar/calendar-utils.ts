import * as Calendar from "expo-calendar";
import { Alert, Platform } from "react-native";
import { showErrorToast, showSuccessToast } from "~/components/toast";
import { parseISO } from "date-fns";
import { CalendarAppointment } from "~/modules/appointments/types/calendar";

/**
 * Request calendar permissions from the user
 * @returns Promise<boolean> - Whether permission was granted
 */
export const requestCalendarPermissions = async (): Promise<boolean> => {
  try {
    // First check current permission status
    const { status: currentStatus } =
      await Calendar.getCalendarPermissionsAsync();

    // If already granted, return true
    if (currentStatus === "granted") {
      return true;
    }

    // Request permissions
    const { status } = await Calendar.requestCalendarPermissionsAsync();

    // If denied, show an alert explaining why we need the permission
    if (status !== "granted") {
      Alert.alert(
        "Calendar Permission Required",
        "To add appointments to your calendar, we need permission to access your calendar. Please enable this in your device settings.",
        [{ text: "OK" }]
      );
      return false;
    }

    return status === "granted";
  } catch (err: unknown) {
    const error = err as Error;
    console.error("Error requesting calendar permissions:", error);
    showErrorToast("Failed to request calendar permissions");
    return false;
  }
};

/**
 * Get the default calendar for the device
 * @returns Promise<string> - The ID of the default calendar
 */
export const getDefaultCalendarId = async (): Promise<string | null> => {
  try {
    // Make sure we have permission first
    const hasPermission = await requestCalendarPermissions();
    if (!hasPermission) {
      return null;
    }

    // Get all calendars
    const calendars = await Calendar.getCalendarsAsync(
      Calendar.EntityTypes.EVENT
    );

    if (calendars.length === 0) {
      // If no calendars are found, create a default calendar on Android
      if (Platform.OS === "android") {
        try {
          const newCalendarId = await Calendar.createCalendarAsync({
            title: "Upace Appointments",
            color: "#1A237E",
            entityType: Calendar.EntityTypes.EVENT,
            sourceId: calendars[0]?.source.id, // Use the first available source
            source: calendars[0]?.source,
            name: "Upace Appointments",
            ownerAccount: "personal",
            accessLevel: Calendar.CalendarAccessLevel.OWNER,
          });
          return newCalendarId;
        } catch (err: unknown) {
          const createError = err as Error;
          console.error("Error creating calendar:", createError);
        }
      }

      showErrorToast("No calendars found on your device");
      return null;
    }

    // Find the default calendar based on platform
    let defaultCalendar;

    if (Platform.OS === "ios") {
      // On iOS, look for iCloud or default calendar
      defaultCalendar = calendars.find(
        (cal) =>
          (cal.source.name === "iCloud" || cal.source.name === "Default") &&
          cal.allowsModifications
      );
    } else {
      // On Android, look for primary calendar
      defaultCalendar = calendars.find(
        (cal) => cal.isPrimary && cal.allowsModifications
      );
    }

    // If no default calendar is found, use the first one that allows modifications
    const firstModifiableCalendar = calendars.find(
      (cal) => cal.allowsModifications
    );

    // If still no calendar is found, use the first calendar
    const calendarId =
      defaultCalendar?.id || firstModifiableCalendar?.id || calendars[0]?.id;

    if (!calendarId) {
      showErrorToast("No suitable calendar found on your device");
    }

    return calendarId || null;
  } catch (err: unknown) {
    const error = err as Error;
    console.error("Error getting default calendar:", error);
    showErrorToast("Failed to access your calendar");
    return null;
  }
};

/**
 * Add an appointment to the device calendar
 * @param title - The title of the event
 * @param startTime - The start time of the event (ISO string)
 * @param endTime - The end time of the event (ISO string)
 * @param location - The location of the event
 * @param notes - Additional notes for the event
 * @returns Promise<string | null> - The ID of the created event, or null if failed
 */
/**
 * Delete a calendar event
 * @param eventId - The ID of the event to delete
 * @returns Promise<boolean> - Whether the deletion was successful
 */
export const deleteCalendarEvent = async (
  eventId: string
): Promise<boolean> => {
  try {
    // Request permissions first
    const hasPermission = await requestCalendarPermissions();
    if (!hasPermission) {
      console.log("Calendar permission is required to delete events");
      return false;
    }

    // Delete the event
    await Calendar.deleteEventAsync(eventId);
    return true;
  } catch (err: unknown) {
    const error = err as Error;
    console.error("Error deleting calendar event:", error);
    return false;
  }
};

/**
 * Update a calendar event
 * @param eventId - The ID of the event to update
 * @param title - The new title of the event
 * @param startTime - The new start time of the event (ISO string)
 * @param endTime - The new end time of the event (ISO string)
 * @param location - The new location of the event
 * @param notes - The new notes for the event
 * @returns Promise<boolean> - Whether the update was successful
 */
export const updateCalendarEvent = async (
  eventId: string,
  title: string,
  startTime: string,
  endTime: string,
  location: string = "",
  notes: string = ""
): Promise<boolean> => {
  try {
    // Request permissions first
    const hasPermission = await requestCalendarPermissions();
    if (!hasPermission) {
      console.log("Calendar permission is required to update events");
      return false;
    }

    // Parse the start and end times
    const startDate = parseISO(startTime);
    const endDate = parseISO(endTime);

    // Create event details object
    const eventDetails: {
      title: string;
      startDate: Date;
      endDate: Date;
      location?: string;
      notes?: string;
      timeZone?: string;
      alarms?: { relativeOffset: number }[];
    } = {
      title,
      startDate,
      endDate,
      location,
      notes,
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };

    // Add alarm only on iOS (can cause issues on some Android devices)
    if (Platform.OS === "ios") {
      eventDetails.alarms = [{ relativeOffset: -30 }]; // 30 minutes before
    }

    // Update the event
    await Calendar.updateEventAsync(eventId, eventDetails);
    return true;
  } catch (err: unknown) {
    const error = err as Error;
    console.error("Error updating calendar event:", error);
    return false;
  }
};

/**
 * Add multiple appointments to the calendar
 * @param appointments - Array of appointment details to add to the calendar
 * @returns Promise<{[appointmentId: string]: string}> - Map of appointment IDs to calendar event IDs
 */
export const addMultipleAppointmentsToCalendar = async (
  appointments: CalendarAppointment[]
): Promise<{ [appointmentId: string]: string }> => {
  try {
    // Request permissions first
    const hasPermission = await requestCalendarPermissions();
    if (!hasPermission) {
      showErrorToast("Calendar permission is required to add events");
      return {};
    }

    // Get the default calendar ID
    const calendarId = await getDefaultCalendarId();
    if (!calendarId) {
      showErrorToast("No calendar found to add events");
      return {};
    }

    const results: { [appointmentId: string]: string } = {};
    let successCount = 0;
    let failureCount = 0;

    // Add each appointment to the calendar
    for (const appointment of appointments) {
      try {
        // Parse the ISO date strings to Date objects
        let startDate, endDate;
        try {
          startDate = parseISO(appointment.startTime);
          endDate = parseISO(appointment.endTime);

          // Validate dates
          if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            throw new Error("Invalid date format");
          }
        } catch (dateError) {
          console.error("Error parsing dates:", dateError, {
            startTime: appointment.startTime,
            endTime: appointment.endTime,
          });

          // Skip this appointment
          failureCount++;
          continue;
        }

        // Create event details object
        const eventDetails: {
          title: string;
          startDate: Date;
          endDate: Date;
          location?: string;
          notes?: string;
          timeZone?: string;
          alarms?: { relativeOffset: number }[];
        } = {
          title: appointment.title,
          startDate,
          endDate,
          location: appointment.location || "",
          notes: appointment.notes || "",
          timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        };

        // Add alarm only on iOS (can cause issues on some Android devices)
        if (Platform.OS === "ios") {
          eventDetails.alarms = [{ relativeOffset: -30 }]; // 30 minutes before
        }

        // Create the event
        const eventId = await Calendar.createEventAsync(
          calendarId,
          eventDetails
        );

        if (eventId) {
          results[appointment.id.toString()] = eventId;
          successCount++;
        } else {
          failureCount++;
        }
      } catch (err) {
        console.error("Error adding event to calendar:", err, { appointment });
        failureCount++;
      }
    }

    // Show a summary toast
    if (successCount > 0) {
      showSuccessToast(
        `Added ${successCount} appointments to calendar${
          failureCount > 0 ? ` (${failureCount} failed)` : ""
        }`
      );
    } else if (failureCount > 0) {
      showErrorToast(`Failed to add ${failureCount} appointments to calendar`);
    }

    return results;
  } catch (err: unknown) {
    const error = err as Error;
    console.error("Error adding multiple events to calendar:", error);
    showErrorToast("Failed to add appointments to calendar");
    return {};
  }
};

export const addAppointmentToCalendar = async (
  title: string,
  startTime: string,
  endTime: string,
  location: string = "",
  notes: string = ""
): Promise<string | null> => {
  try {
    // Request permissions first
    const hasPermission = await requestCalendarPermissions();
    if (!hasPermission) {
      showErrorToast("Calendar permission is required to add events");
      return null;
    }

    // Get the default calendar ID
    const calendarId = await getDefaultCalendarId();
    if (!calendarId) {
      showErrorToast("No calendar found to add event");
      return null;
    }

    // Parse the ISO date strings to Date objects
    let startDate, endDate;
    try {
      startDate = parseISO(startTime);
      endDate = parseISO(endTime);

      // Validate dates
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        throw new Error("Invalid date format");
      }
    } catch (dateError) {
      console.error("Error parsing dates:", dateError, { startTime, endTime });

      // Fallback to current time + 1 hour if dates are invalid
      const now = new Date();
      startDate = now;
      endDate = new Date(now.getTime() + 60 * 60 * 1000); // 1 hour later
    }

    // Create event details object
    const eventDetails: {
      title: string;
      startDate: Date;
      endDate: Date;
      location?: string;
      notes?: string;
      timeZone?: string;
      alarms?: { relativeOffset: number }[];
    } = {
      title,
      startDate,
      endDate,
      location,
      notes,
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };

    // Add alarm only on iOS (can cause issues on some Android devices)
    if (Platform.OS === "ios") {
      eventDetails.alarms = [{ relativeOffset: -30 }]; // 30 minutes before
    }

    // Create the event
    const eventId = await Calendar.createEventAsync(calendarId, eventDetails);

    if (!eventId) {
      throw new Error("Failed to create calendar event");
    }

    return eventId;
  } catch (err: unknown) {
    const error = err as Error;
    console.error("Error adding event to calendar:", error);

    // Provide more specific error messages
    if (error.message?.includes("permission")) {
      showErrorToast("Calendar permission denied. Please enable in settings.");
    } else if (error.message?.includes("calendar")) {
      showErrorToast("Calendar error: " + error.message);
    } else {
      showErrorToast("Failed to add appointment to calendar");
    }

    return null;
  }
};
