import AsyncStorage from "@react-native-async-storage/async-storage";

const CALENDAR_EVENTS_KEY = "upace_calendar_events";

interface CalendarEventMapping {
  [appointmentId: string]: string; // appointmentId -> calendarEventId
}

/**
 * Save a calendar event ID for an appointment
 * @param appointmentId - The ID of the appointment
 * @param calendarEventId - The ID of the calendar event
 */
export const saveCalendarEventId = async (
  appointmentId: string | number,
  calendarEventId: string
): Promise<void> => {
  try {
    // Get existing mappings
    const existingMappingsStr = await AsyncStorage.getItem(CALENDAR_EVENTS_KEY);
    const existingMappings: CalendarEventMapping = existingMappingsStr
      ? JSON.parse(existingMappingsStr)
      : {};

    // Add new mapping
    existingMappings[appointmentId.toString()] = calendarEventId;

    // Save updated mappings
    await AsyncStorage.setItem(
      CALENDAR_EVENTS_KEY,
      JSON.stringify(existingMappings)
    );

    console.log(
      `Saved calendar event ID ${calendarEventId} for appointment ${appointmentId}`
    );
  } catch (err) {
    console.error("Error saving calendar event ID:", err);
  }
};

/**
 * Save multiple calendar event IDs for appointments
 * @param eventMappings - Object mapping appointment IDs to calendar event IDs
 */
export const saveMultipleCalendarEventIds = async (eventMappings: {
  [appointmentId: string]: string;
}): Promise<void> => {
  try {
    // Get existing mappings
    const existingMappingsStr = await AsyncStorage.getItem(CALENDAR_EVENTS_KEY);
    const existingMappings: CalendarEventMapping = existingMappingsStr
      ? JSON.parse(existingMappingsStr)
      : {};

    // Add new mappings
    const updatedMappings = {
      ...existingMappings,
      ...eventMappings,
    };

    // Save updated mappings
    await AsyncStorage.setItem(
      CALENDAR_EVENTS_KEY,
      JSON.stringify(updatedMappings)
    );

    console.log(
      `Saved ${Object.keys(eventMappings).length} calendar event IDs`
    );
  } catch (err) {
    console.error("Error saving multiple calendar event IDs:", err);
  }
};

/**
 * Get the calendar event ID for an appointment
 * @param appointmentId - The ID of the appointment
 * @returns The calendar event ID, or null if not found
 */
export const getCalendarEventId = async (
  appointmentId: string | number
): Promise<string | null> => {
  try {
    // Get existing mappings
    const existingMappingsStr = await AsyncStorage.getItem(CALENDAR_EVENTS_KEY);
    if (!existingMappingsStr) {
      return null;
    }

    const existingMappings: CalendarEventMapping =
      JSON.parse(existingMappingsStr);
    return existingMappings[appointmentId.toString()] || null;
  } catch (err) {
    console.error("Error getting calendar event ID:", err);
    return null;
  }
};

/**
 * Remove a calendar event ID for an appointment
 * @param appointmentId - The ID of the appointment
 */
export const removeCalendarEventId = async (
  appointmentId: string | number
): Promise<void> => {
  try {
    // Get existing mappings
    const existingMappingsStr = await AsyncStorage.getItem(CALENDAR_EVENTS_KEY);
    if (!existingMappingsStr) {
      return;
    }

    const existingMappings: CalendarEventMapping =
      JSON.parse(existingMappingsStr);

    // Remove mapping
    delete existingMappings[appointmentId.toString()];

    // Save updated mappings
    await AsyncStorage.setItem(
      CALENDAR_EVENTS_KEY,
      JSON.stringify(existingMappings)
    );

    console.log(`Removed calendar event ID for appointment ${appointmentId}`);
  } catch (err) {
    console.error("Error removing calendar event ID:", err);
  }
};
