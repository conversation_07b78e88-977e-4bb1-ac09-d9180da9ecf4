import * as React from "react";
import { View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import { LoginBackground } from "~/components/modules/login/login-bg";
import LoginCard from "~/components/modules/login/login-card";
import { AuthText } from "~/components/ui/auth-text";
import { useSession } from "~/modules/login/auth-provider";
import { ScreenTracker } from "~/components/analytics/ScreenTracker";

import { LoginForm } from "~/modules/login/login";

export default function Login() {
  const { signIn, isError, error, isLoading } = useSession();

  return (
    <ScreenTracker screenName="Login">
      <LoginBackground>
        <KeyboardAwareScrollView
          bottomOffset={10}
          contentContainerStyle={{
            gap: 16,
            paddingBottom: 30,
            flex: 1,
          }}
        >
          <LoginCard>
            <AuthText
              className="text-3xl font-extrabold text-[#069CC3]"
              maxFontScale={1.1}
            >
              WELCOME
            </AuthText>

            <AuthText className="mb-6 mt-1 dark:text-black" maxFontScale={1.2}>
              Login with your email address associated with your admin account.
            </AuthText>
            <View className="w-full">
              <LoginForm
                handleLogin={signIn}
                isLoading={isLoading}
                isError={isError}
                errorMessage={error?.message}
              />
            </View>
          </LoginCard>
        </KeyboardAwareScrollView>
      </LoginBackground>
    </ScreenTracker>
  );
}
