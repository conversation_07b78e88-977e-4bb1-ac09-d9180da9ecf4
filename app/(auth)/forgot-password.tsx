import * as React from "react";
import { View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import { LoginBackground } from "~/components/modules/login/login-bg";
import LoginCard from "~/components/modules/login/login-card";
import { AuthText } from "~/components/ui/auth-text";
import { ForgotForm } from "~/modules/login/forgot";

export default function Login() {
  return (
    <LoginBackground>
      <KeyboardAwareScrollView
        bottomOffset={10}
        contentContainerStyle={{
          gap: 16,
          paddingBottom: 30,
          flex: 1,
        }}
      >
        <LoginCard>
          <AuthText
            className="text-3xl font-extrabold text-[#069CC3]"
            maxFontScale={1.1}
          >
            FORGOT PASSWORD?
          </AuthText>

          <AuthText className="mb-6 mt-1 dark:text-black" maxFontScale={1.2}>
            Enter your Upace admin email, and we’ll send you instructions to
            reset your password.
          </AuthText>
          <View className="w-full">
            <ForgotForm />
          </View>
        </LoginCard>
      </KeyboardAwareScrollView>
    </LoginBackground>
  );
}
