import { Fragment, useState } from "react";
import { Camera } from "~/components/camera";
import { useCheckInSearch } from "~/modules/classes/mutations/useCheckInSearch";
import { useSession } from "~/modules/login/auth-provider";
import { useBarcodeCheckIn } from "~/modules/classes/mutations/useBarcodeCheckIn";
import { useLocalSearchParams } from "expo-router";
import { View } from "react-native";
import { Button } from "~/components/ui/button";
import { Alert, AlertDescription } from "~/components/ui/alert";

function SuccessComponent({
  onScanAgain,
  message,
  isError,
}: {
  onScanAgain: () => void;
  message?: string;
  isError?: boolean;
}) {
  return (
    <View className="flex-1 items-center p-2 justify-center">
      <Alert variant={isError ? "destructive" : "default"}>
        <AlertDescription className="text-lg">{message}</AlertDescription>
      </Alert>
      <Button
        variant={isError ? "destructive" : "default"}
        onPress={onScanAgain}
        className="mt-4"
        label={isError ? "Try again" : "Scan Again"}
      />
    </View>
  );
}

export default function BarcodePage() {
  const { data: sessionData } = useSession();

  const { id } = useLocalSearchParams<{ id: string }>();

  const { mutateAsync: searchBarcode, isError } = useCheckInSearch();

  const {
    mutateAsync: barcodeCheckIn,
    isError: isScanError,
    data: barcodeData,
    error,
  } = useBarcodeCheckIn();

  const [showCamera, setShowCamera] = useState(true);

  const handleCheckIn = async (data: string) => {
    const response = await searchBarcode({
      barcode: data,
      uniId: String(sessionData?.university_id),
    });

    barcodeCheckIn({
      classId: Number(id),
      // @ts-expect-error
      userId: response?.[0]?.id,
      uniId: String(sessionData?.university_id),
    });

    setShowCamera(false);
  };

  const handleScanAgain = () => {
    setShowCamera(true);
  };

  const hasError = isError || isScanError;

  return (
    <Fragment>
      {!showCamera ? (
        <SuccessComponent
          onScanAgain={handleScanAgain}
          message={barcodeData?.message || error?.message}
          isError={hasError}
        />
      ) : (
        <Camera onBarcodeScanned={handleCheckIn} />
      )}
    </Fragment>
  );
}
