import { ScrollView, <PERSON> } from "react-native";

import { Text } from "~/components/ui/text";

import { BottomSheet } from "~/components/modules/classes/bottom-sheet";
import { useClassByIdQuery } from "~/modules/classes/queries/useClassbyIdQuery";
import { Celebration } from "~/modules/classes/types";

import { shouldShowNotification } from "~/components/modules/classes/celebrations/utils";

const CelebrationList = ({
  title,
  celebrations,
}: {
  title?: string;
  celebrations?: Celebration[];
}) =>
  celebrations?.length ? (
    <View className="mb-4">
      <View className="flex items-center justify-center">
        <Text className="text-lg text-blue-500 font-bold ">{title}</Text>
      </View>

      {celebrations?.map((celebration) => (
        <Text
          key={celebration?.id}
          className="mt-3 font-bold text-gray-600 dark:text-white text-center"
        >
          {celebration?.name ||
            `${celebration?.first_name} ${celebration?.last_name}`}
        </Text>
      ))}
    </View>
  ) : null;

export default function Notifications() {
  const {
    data: {
      celebrations: {
        BIRTHDAYS = [],
        FIRST_CLASS = [],
        NEW_MEMBER = [],
        MILESTONES = [],
      } = {},
    } = {},
  } = useClassByIdQuery();

  const showNotification = shouldShowNotification({
    BIRTHDAYS,
    FIRST_CLASS,
    NEW_MEMBER,
    MILESTONES,
  });

  return (
    <BottomSheet title="Reasons to celebrate today" height={"50%"}>
      <View className="mt-10">
        {!showNotification ? (
          <View>
            <Text className="text-center">There are no notifications</Text>
          </View>
        ) : (
          <ScrollView className="mt-2">
            <CelebrationList
              title="Today's Birthdays"
              celebrations={BIRTHDAYS}
            />
            <CelebrationList
              title="Welcome New Members"
              celebrations={NEW_MEMBER}
            />
            <CelebrationList title="1st Class" celebrations={FIRST_CLASS} />
            <CelebrationList title="Milestones" celebrations={MILESTONES} />
          </ScrollView>
        )}
      </View>
    </BottomSheet>
  );
}
