import { format } from "date-fns";
import { useLocalSearchParams, useNavigation } from "expo-router";
import { View } from "react-native";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Text } from "~/components/ui/text";

import {
  KeyboardAwareScrollView,
  KeyboardToolbar,
} from "react-native-keyboard-controller";
import { useUpdateOccupancy } from "~/modules/classes/mutations/useUpdateOccupancy";
import { useState } from "react";
import { BottomSheet } from "~/components/modules/classes/bottom-sheet";

export default function Occupancy() {
  const navigation = useNavigation();

  const { total_occupancy, last_updated, updated_by, id, date } =
    useLocalSearchParams<{
      total_occupancy?: string;
      last_updated?: string;
      updated_by?: string;
      walkInSpots?: string;
      id: string;
      date?: string;
    }>();

  const [value, setValue] = useState(total_occupancy);

  const { mutate: updateOccupancy, isPending } = useUpdateOccupancy(() =>
    navigation.goBack()
  );

  const handleSubmit = () => {
    if (!value) return;
    return updateOccupancy({
      class_id: id,
      total: Number(value),
      date: date as string,
    });
  };

  return (
    <BottomSheet>
      <View className="gap-3 pt-4">
        <Text className="text-center font-bold text-2xl mb-4">
          Update Occupancy
        </Text>

        <KeyboardAwareScrollView
          bottomOffset={10}
          contentContainerStyle={{
            gap: 16,
            paddingTop: 16,
            paddingBottom: 30,
          }}
        >
          <Input
            keyboardType="numeric"
            value={value ?? ""}
            onChangeText={setValue}
          />

          {updated_by && (
            <View className="flex flex-row gap-4 items-center text-center mb-2">
              <Text className="text-lg">Updated by:</Text>
              <Text className="font-bold text-lg">{updated_by}</Text>
            </View>
          )}

          {last_updated && (
            <View className="flex flex-row gap-4 items-center">
              <Text>Last updated:</Text>
              {last_updated && (
                <Text className="text-center font-bold ">
                  {format(last_updated ?? "", "MM/d/yy, h:mmaa")}
                </Text>
              )}
            </View>
          )}
          <Button
            label="Save"
            onPress={handleSubmit}
            isLoading={isPending}
            disabled={isPending}
            className="mt-4"
          />
        </KeyboardAwareScrollView>
        <KeyboardToolbar />
      </View>
    </BottomSheet>
  );
}
