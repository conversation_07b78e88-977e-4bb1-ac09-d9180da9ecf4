import { router, Stack, useLocalSearchParams } from "expo-router";

import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { useColorScheme } from "~/lib/useColorScheme";
import { View } from "react-native";
import { useClassByIdQuery } from "~/modules/classes/queries/useClassbyIdQuery";
import { shouldShowNotification } from "~/components/modules/classes/celebrations/utils";

export default function DetailsLayout() {
  const { isDarkColorScheme } = useColorScheme();

  const { id, date } = useLocalSearchParams<{ id: string; date?: string }>();

  const {
    data: {
      celebrations: {
        BIRTHDAYS = [],
        FIRST_CLASS = [],
        NEW_MEMBER = [],
        MILESTONES = [],
      } = {},
    } = {},
  } = useClassByIdQuery();

  const shouldNotification = shouldShowNotification({
    BIRTHDAYS,
    FIRST_CLASS,
    NEW_MEMBER,
    MILESTONES,
  });

  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          title: "Class Details",
          headerRight: () => (
            <View style={{ position: "relative" }}>
              <MaterialIcons
                name="notifications"
                size={20}
                color={"#069CC3"}
                onPress={() =>
                  router.navigate({
                    pathname: "/(classes)/[id]/notifications",
                    params: { id, date },
                  })
                }
                accessibilityRole="button"
                accessibilityLabel="View notifications"
                accessibilityHint="Shows class celebrations and notifications"
              />
              {shouldNotification && (
                <View
                  style={{
                    position: "absolute",
                    right: 0,
                    top: 0,
                    width: 7,
                    height: 7,
                    borderRadius: 5,
                    backgroundColor: "#E93B92",
                  }}
                />
              )}
            </View>
          ),
          headerLeft: () => (
            <MaterialIcons
              name="arrow-back"
              size={20}
              color={isDarkColorScheme ? "white" : "dark"}
              onPress={router.back}
              accessibilityRole="button"
              accessibilityLabel="Go back"
              accessibilityHint="Returns to previous screen"
            />
          ),
        }}
      />

      <Stack.Screen
        name="notifications"
        options={{
          presentation: "transparentModal",
          animation: "slide_from_bottom",
          animationTypeForReplace: "pop",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="occupancy"
        options={{
          presentation: "transparentModal",
          animation: "slide_from_bottom",
          animationTypeForReplace: "pop",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="add-reservation"
        options={{
          presentation: "transparentModal",
          animation: "slide_from_bottom",
          animationTypeForReplace: "pop",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="barcode"
        options={{
          title: "Scan Barcode to check in",
          headerShown: true,
          headerLeft() {
            return (
              <MaterialIcons
                name="arrow-back"
                size={20}
                color={isDarkColorScheme ? "white" : "dark"}
                onPress={router.back}
                accessibilityRole="button"
                accessibilityLabel="Go back"
                accessibilityHint="Returns to previous screen"
              />
            );
          },
        }}
      />
    </Stack>
  );
}
