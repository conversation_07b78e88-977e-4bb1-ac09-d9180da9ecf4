import { router, Stack } from "expo-router";

import MaterialIcons from "@expo/vector-icons/MaterialIcons";

import { useColorScheme } from "~/lib/useColorScheme";

export default function SubRequest() {
  const { isDarkColorScheme } = useColorScheme();

  return (
    <Stack>
      <Stack.Screen
        name="request"
        options={{
          title: "REQUEST A SUB",
          headerTitleStyle: {
            color: isDarkColorScheme ? "white" : "#002966",
          },
          headerLeft: () => (
            <MaterialIcons
              name="arrow-back"
              size={20}
              color={isDarkColorScheme ? "white" : "dark"}
              onPress={router.back}
            />
          ),
        }}
      />
    </Stack>
  );
}
