import * as z from "zod";

import {
  RequestSubManagementForm,
  RequestSubSchema,
} from "~/modules/sub-management/request-sub-form";
import { useCreateSubRequest } from "~/modules/sub-management/mutations/useCreateSubRequest";
import { router } from "expo-router";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import { ScreenTracker } from "~/components/analytics/ScreenTracker";

export default function SubRequest() {
  const { mutate: createSubRequest, isPending } = useCreateSubRequest(() =>
    router.push("/(classes)/(tabs)/subs")
  );
  const { trackEvent, EVENTS } = useAnalytics();

  const onSubmit = (formData: z.infer<typeof RequestSubSchema>) => {
    createSubRequest(formData);

    // Track sub request creation
    trackEvent(EVENTS.CREATE_SUB_REQUEST, {
      class_ids: formData.classes.map((cls) => cls.id).join(","),
      class_names: formData.classes.map((cls) => cls.title).join(","),
      request_date: formData.date,
      reason: formData.reason.title,
      multiple_classes: formData.classes.length > 1,
    });
  };

  return (
    <ScreenTracker screenName="CreateSubRequest">
      <RequestSubManagementForm isLoading={isPending} onSubmit={onSubmit} />
    </ScreenTracker>
  );
}
