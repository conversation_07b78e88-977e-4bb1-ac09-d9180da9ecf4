import React from "react";
import { View } from "react-native";
import { router } from "expo-router";

import { useCancelClassMutation } from "~/modules/cancelled-classes/mutations/useCancelClassMutation";
import { ScreenTracker } from "~/components/analytics/ScreenTracker";

import * as z from "zod";

import {
  CancelClassForm,
  CancelClassSchema,
} from "~/components/modules/cancelled-classes/cancel-class-form";
import { useLocalSearchParams } from "expo-router";

export default function CreateCancelledClassPage() {
  const { mutate: cancelClass, isPending } = useCancelClassMutation(() => {
    router.back();
  });

  const { id } = useLocalSearchParams<{
    id: string;
  }>();

  const handleSubmit = async (formData: z.infer<typeof CancelClassSchema>) => {
    const slotIds = formData.classes.map((cls) => Number(cls.id));
    cancelClass({
      date: formData.date,
      reason: formData.reason,
      slot_ids: slotIds,
      cancellation_id: id,
    });
  };

  return (
    <ScreenTracker screenName="Create Cancelled Class">
      <View className="flex-1 bg-background">
        <CancelClassForm isLoading={isPending} onSubmit={handleSubmit} />
      </View>
    </ScreenTracker>
  );
}
