import { useLocalSearchParams } from "expo-router";

import { AppCard } from "~/components/modules/appointments/app-card";
import { Text } from "~/components/ui/text";
import { useMemo, useState } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "~/components/ui/tabs";
import { useReservations } from "~/modules/appointments/queries/useReservations";
import { Loader } from "~/components/modules/classes/loader";
import { useAppointments } from "~/modules/appointments/queries/useAppointments";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
} from "~/components/ui/card";
import { obtainDateFrame } from "~/modules/classes/utils";
import { FlashList } from "@shopify/flash-list";
import { format, parseISO } from "date-fns";
import { DATE_FORMAT } from "~/constants/date-formats";
import { useCheckInMutation } from "~/modules/classes/mutations/useCheckInMutation";

import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import {
  RefreshControl,
  View,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import { ApptUser } from "~/components/modules/appointments/appt-user";

import AntDesign from "@expo/vector-icons/AntDesign";
import { AppointmentEditModal } from "~/components/modules/appointments/appointment-edit-modal";
import { Appointment } from "~/modules/appointments/types";
import { Reservation } from "~/modules/classes/types";

enum AppType {
  UPCOMING = "upcoming",
  USED = "used",
}

const AppReserveCard = ({
  start_time,
  end_time,
  equipment_name,
  room_name,
  first_name,
  last_name,
  id,
  checkin,
  image,
  topRightContent,
  membership_id,
  equipment_id,
  user_id,
  onRefresh,
}: {
  id: number;
  start_time: string;
  end_time: string;
  equipment_name?: string;
  room_name?: string;
  first_name?: string;
  last_name?: string;
  checkin?: number;
  image?: string;
  topRightContent?: React.ReactNode;
  membership_id?: string | null;
  equipment_id?: number | string;
  user_id?: number;
  onRefresh?: () => void;
}) => {
  const formattedDate = format(parseISO(start_time), DATE_FORMAT.DAY_MONTH_DAY);

  return (
    <Card className="w-full mt-4 relative">
      {topRightContent && (
        <View className="absolute top-2 right-2 z-10">{topRightContent}</View>
      )}
      <CardHeader className="pb-1 pt-4">
        <CardDescription className="pb-0">
          {`${formattedDate} ${obtainDateFrame(start_time, end_time)}`}
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0 flex flex-row justify-between">
        <CardContent className="pb-3">
          <Text className="font-bold">{equipment_name}</Text>
          <Text>Member: {`${first_name} ${last_name}`}</Text>
          <Text>{room_name}</Text>
        </CardContent>
      </CardContent>
      <ApptUser
        date={start_time?.split(" ")[0] ?? ""}
        name={`${first_name} ${last_name}`}
        id={id}
        isCheckIn={Boolean(checkin)}
        image={image}
        membership_id={membership_id}
        equipment_id={equipment_id}
        user_id={user_id}
        onRefresh={onRefresh}
      />
    </Card>
  );
};

const ReservationsTabs = ({
  membership_id,
  foundAppointment,
  refreshAppointment,
  refreshReservations,
}: {
  membership_id: string;
  foundAppointment: Appointment;
  refreshAppointment: () => void;
  refreshReservations: () => void;
}) => {
  const [value, setValue] = useState(AppType.UPCOMING);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedReservation, setSelectedReservation] =
    useState<Reservation | null>(null);

  const { data: reservations, isPending } = useReservations({
    membership_id,
    type: "pt",
  });

  const upcomingReservations = useMemo(
    () => reservations?.filter((res) => res.checkin === 0) ?? [],
    [reservations]
  );

  const usedReservations = useMemo(
    () => reservations?.filter((res) => res.checkin === 1) ?? [],
    [reservations]
  );

  if (!membership_id) {
    return (
      <Text className="font-extrabold text-center mt-6 text-lg">
        No membership available
      </Text>
    );
  }

  if (isPending) {
    return <Loader size="large" />;
  }

  return (
    <>
      <Tabs
        value={value}
        onValueChange={(newValue) => setValue(newValue as AppType)}
      >
        <TabsList className="flex-row gap-2 mt-4">
          <TabsTrigger
            value={AppType.UPCOMING}
            className="flex-1 rounded-lg"
            label={`UPCOMING (${upcomingReservations.length})`}
          />

          <TabsTrigger
            value={AppType.USED}
            className="flex-1 rounded-lg"
            label={`USED (${usedReservations.length})`}
          />
        </TabsList>

        <TabsContent className="flex-1" value={AppType.UPCOMING}>
          <FlashList
            data={upcomingReservations}
            renderItem={({ item }) => (
              <AppReserveCard
                {...item}
                onRefresh={() => {
                  refreshAppointment();
                  refreshReservations();
                }}
                topRightContent={
                  !item.checkin && (
                    <TouchableOpacity
                      onPress={() => {
                        setSelectedReservation(item);
                        setIsEditModalOpen(true);
                      }}
                    >
                      <AntDesign name="edit" size={15} color="#1A237E" />
                    </TouchableOpacity>
                  )
                }
              />
            )}
            estimatedItemSize={200}
          />
        </TabsContent>
        <TabsContent className="flex-1" value={AppType.USED}>
          <FlashList
            data={usedReservations}
            renderItem={({ item }) => (
              <AppReserveCard
                {...item}
                onRefresh={() => {
                  refreshAppointment();
                  refreshReservations();
                }}
              />
            )}
            estimatedItemSize={200}
          />
        </TabsContent>
      </Tabs>

      {isEditModalOpen && selectedReservation && (
        <AppointmentEditModal
          isOpen={isEditModalOpen}
          setIsOpen={setIsEditModalOpen}
          equipment_id={foundAppointment?.equipment_id as number}
          reservationId={Number(selectedReservation.id)}
          userId={foundAppointment?.user_id as number}
          currentDate={selectedReservation.start_time?.split(" ")[0]}
          onSuccess={() => {
            refreshAppointment();
            refreshReservations();
            setSelectedReservation(null);
          }}
        />
      )}
    </>
  );
};

const AppointmentContent = ({
  id,
  membership_id,
  foundAppointment,
  handleNoShow,
  isPending,
  refreshAppointment,
  refreshReservations,
}: {
  id: string;
  membership_id: string;
  foundAppointment: Appointment;
  handleNoShow: () => void;
  isPending: boolean;
  refreshAppointment: () => void;
  refreshReservations: () => void;
}) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  return (
    <View className="p-4">
      <View>
        <AppCard
          {...foundAppointment}
          id={Number(id)}
          onRefresh={() => {
            refreshAppointment();
            refreshReservations();
          }}
          topRightContent={
            !foundAppointment?.checkin && (
              <TouchableOpacity onPress={() => setIsEditModalOpen(true)}>
                <AntDesign name="edit" size={20} color="#1A237E" />
              </TouchableOpacity>
            )
          }
          extraActions={
            <View className="flex flex-row items-center gap-2">
              {membership_id && (
                <View className="relative items-center">
                  <Text className="text-[5px]">No Show</Text>
                  <View className="relative items-center justify-center">
                    <MaterialIcons
                      disabled={
                        isPending ||
                        Boolean(foundAppointment?.is_no_show) ||
                        Boolean(foundAppointment?.checkin)
                      }
                      onPress={handleNoShow}
                      name="person-off"
                      size={20}
                      color={
                        Boolean(foundAppointment?.is_no_show)
                          ? "#4CAF50"
                          : "#e57373"
                      }
                      style={{ opacity: isPending ? 0.5 : 1 }}
                    />
                    {isPending && (
                      <View className="absolute inset-0 flex items-center justify-center">
                        <Loader size="small" />
                      </View>
                    )}
                  </View>
                  <Text className="text-[5px]">Check-in</Text>
                </View>
              )}
            </View>
          }
        />
      </View>

      <View className="mt-4 mb-4-">
        <Text className="text-lg font-extrabold text-[#1A237E] dark:text-white">
          PACKAGE
        </Text>
        <Text>{`${foundAppointment?.first_name} ${foundAppointment?.last_name}`}</Text>
        <Text className="font-extrabold">{`${
          foundAppointment?.membership_package ?? "No memberships selected"
        }`}</Text>
        <Text>{foundAppointment?.room_name}</Text>
        <Text>{foundAppointment?.pt_equipment_name ?? ""}</Text>
      </View>

      <ReservationsTabs
        membership_id={membership_id}
        foundAppointment={foundAppointment}
        refreshAppointment={refreshAppointment}
        refreshReservations={refreshReservations}
      />
      {isEditModalOpen && (
        <AppointmentEditModal
          isOpen={isEditModalOpen}
          setIsOpen={setIsEditModalOpen}
          equipment_id={foundAppointment?.equipment_id as number}
          reservationId={Number(id)}
          userId={foundAppointment?.user_id as number}
          currentDate={foundAppointment?.start_time?.split(" ")[0]}
          onSuccess={() => {
            refreshAppointment();
            refreshReservations();
          }}
        />
      )}
    </View>
  );
};

export default function AppointmentDetails() {
  const { id, membership_id } = useLocalSearchParams<Record<string, string>>();

  const {
    data: appointmentData,
    refetch: refreshAppointment,
    isPending: isAppointmentLoading,
  } = useAppointments();

  const { refetch: refreshReservations, isPending: isReservationLoading } =
    useReservations({
      membership_id,
      type: "pt",
    });

  const foundAppointment = appointmentData?.find(
    (rec) => rec.id === Number(id)
  );

  const { mutate: handleCheckIn, isPending } = useCheckInMutation();

  const handleNoShow = () => {
    handleCheckIn({
      id,
      isCheckIn: true,
      is_no_show: 1,
      type: "pt",
    });
  };

  const handleRefresh = async () => {
    await Promise.all([refreshAppointment(), refreshReservations()]);
  };

  if (!foundAppointment) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text>Appointment not found</Text>
      </View>
    );
  }

  const data = [1]; // Single item to render the content

  return (
    <SafeAreaView className="flex-1">
      <FlashList
        data={data}
        renderItem={() => (
          <AppointmentContent
            id={id}
            membership_id={membership_id}
            foundAppointment={foundAppointment}
            handleNoShow={handleNoShow}
            isPending={isPending}
            refreshAppointment={refreshAppointment}
            refreshReservations={refreshReservations}
          />
        )}
        estimatedItemSize={800}
        refreshControl={
          <RefreshControl
            refreshing={isAppointmentLoading || isReservationLoading}
            onRefresh={handleRefresh}
          />
        }
      />
    </SafeAreaView>
  );
}
