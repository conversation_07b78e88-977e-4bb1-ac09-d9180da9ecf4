import { router, Stack } from "expo-router";

import MaterialIcons from "@expo/vector-icons/MaterialIcons";

import { useColorScheme } from "~/lib/useColorScheme";

export default function Appointment() {
  const { isDarkColorScheme } = useColorScheme();

  return (
    <Stack>
      <Stack.Screen
        name="request"
        options={{
          title: "ADD APPOINTMENT",
          headerTitleStyle: {
            color: isDarkColorScheme ? "white" : "#002966",
          },
          headerLeft: () => (
            <MaterialIcons
              name="arrow-back"
              size={20}
              color={isDarkColorScheme ? "white" : "#002966"}
              onPress={router.back}
            />
          ),
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: "Appointment Details",
          headerTitleStyle: {
            color: isDarkColorScheme ? "white" : "#002966",
          },
          headerLeft: () => (
            <MaterialIcons
              name="arrow-back"
              size={20}
              color={isDarkColorScheme ? "white" : "#002966"}
              onPress={router.back}
            />
          ),
        }}
      />
    </Stack>
  );
}
