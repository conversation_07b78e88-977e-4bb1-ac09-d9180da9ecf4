import { FlashList } from "@shopify/flash-list";
import { format, startOfMonth, parseISO } from "date-fns";
import { groupBy } from "lodash/fp";
import { matchSorter } from "match-sorter";
import { Fragment, useState } from "react";
import { TouchableOpacity, View } from "react-native";
import { AppCard } from "~/components/modules/appointments/app-card";
import AntDesign from "@expo/vector-icons/AntDesign";
import { ClassCardSkeleton } from "~/components/modules/classes/card-skeleton";
import { SearchInput } from "~/components/modules/classes/search-input";
import { WelcomeSection } from "~/components/modules/common/welcome";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { useAppointments } from "~/modules/appointments/queries/useAppointments";

import { EmptyState } from "~/components/modules/classes/reservations";
import { router } from "expo-router";
import { MonthDatePicker } from "~/components/modules/common/month-picker";
import { createSearchFields } from "~/utils/search";
import { AppointmentEditModal } from "~/components/modules/appointments/appointment-edit-modal";

import { Appointment } from "~/modules/appointments/types";

const appointmentSearchFields = createSearchFields<Appointment>([
  "equipment_name",
  "room_name",
  "first_name",
  "last_name",
  "gym_name",
]);

const obtainEmptyState = (searchTerm: string, selectedDate?: Date) => {
  if (searchTerm) {
    return `No appointments found for ${searchTerm}`;
  }
  if (selectedDate) {
    return `No appointments found for ${format(selectedDate, "MMMM yyyy")}`;
  }
  return "You don't have any appointments";
};

export default function Appointments() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedAppointment, setSelectedAppointment] =
    useState<Appointment | null>(null);

  const { data, isPending, refetch } = useAppointments(selectedDate);

  const filteredData = searchTerm
    ? matchSorter(data ?? [], searchTerm, {
        keys: appointmentSearchFields,
      })
    : data;

  const groupByMonth = Object.entries(
    groupBy(
      (rec) => format(parseISO(rec.start_time.split(" ")[0]), "LLLL yyyy"),
      filteredData
    )
  );

  const renderMonthSection = () =>
    groupByMonth?.map((month, index) => (
      <View key={month[0]}>
        {index > 0 && (
          <View className="flex flex-row justify-between items-center">
            <Text className="font-extrabold pb-3 mt-2 text-lg dark:text-orange-300 text-[#1A237E]">
              {month[0].toUpperCase()}
            </Text>
          </View>
        )}
        <FlashList
          data={month[1]}
          renderItem={({ item }) => (
            <TouchableOpacity
              onPress={() =>
                router.push({
                  pathname: "/(classes)/(appointment)/[id]",
                  params: {
                    ...item,
                  },
                })
              }
            >
              <AppCard
                {...item}
                onRefresh={refetch}
                topRightContent={
                  !item.checkin && (
                    <TouchableOpacity
                      onPress={(e) => {
                        e.stopPropagation();
                        setSelectedAppointment(item);
                        setIsEditModalOpen(true);
                      }}
                    >
                      <AntDesign name="edit" size={20} color="#1A237E" />
                    </TouchableOpacity>
                  )
                }
              />
            </TouchableOpacity>
          )}
          estimatedItemSize={80}
        />
      </View>
    ));

  return (
    <Fragment>
      <View className="p-6 flex flex-1">
        <WelcomeSection
          count={data?.length || 0}
          salutation="Hello"
          message="UPCOMING APPOINTMENTS"
        />
        <View className="flex flex-row justify-between">
          <SearchInput
            className="w-[74%]"
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            placeholder="Search by name, day, or time"
            searchContext="appointments"
          />
          <Button
            onPress={() => router.push("/(appointment)/request")}
            className="bg-[#002966] mt-3"
            label="Add Appt"
            accessibilityLabel="Add Appointment"
          />
        </View>

        <View className="flex flex-row justify-between items-center my-4">
          <Text className="font-extrabold text-lg dark:text-orange-300 text-[#1A237E]">
            {format(
              selectedDate ??
                (groupByMonth.length > 0
                  ? parseISO(groupByMonth[0][1][0].start_time.split(" ")[0])
                  : new Date()),
              "MMMM yyyy"
            ).toUpperCase()}
          </Text>
          <MonthDatePicker
            value={
              selectedDate ??
              (groupByMonth.length > 0
                ? parseISO(groupByMonth[0][1][0].start_time.split(" ")[0])
                : new Date())
            }
            modalTitle="Select Month & Year"
            onChange={(date) => {
              setSelectedDate(date);
              router.push({
                pathname: "/(classes)/(tabs)/appointments",
                params: {
                  selectedDate: date
                    ? startOfMonth(date).toISOString()
                    : undefined,
                },
              });
            }}
          />
        </View>

        {isPending ? (
          <View className="mt-4 w-full">
            {Array.from({ length: 5 }).map((_, index) => (
              <ClassCardSkeleton key={index} />
            ))}
          </View>
        ) : !filteredData?.length ? (
          <EmptyState
            label={obtainEmptyState(searchTerm, selectedDate)}
            action={
              !searchTerm &&
              selectedDate && (
                <Button
                  className="bg-[#002966] mt-3"
                  onPress={() => setSelectedDate(undefined)}
                  label="Back to today"
                />
              )
            }
          />
        ) : (
          <FlashList
            className="mt-4 w-full"
            data={[{ key: "list" }]}
            renderItem={() => <>{renderMonthSection()}</>}
            estimatedItemSize={80}
            onRefresh={refetch}
            refreshing={isPending}
          />
        )}
      </View>

      {isEditModalOpen && selectedAppointment && (
        <AppointmentEditModal
          isOpen={isEditModalOpen}
          setIsOpen={setIsEditModalOpen}
          equipment_id={selectedAppointment.equipment_id as number}
          reservationId={Number(selectedAppointment.id)}
          userId={selectedAppointment.user_id as number}
          currentDate={selectedAppointment.start_time?.split(" ")[0]}
          onSuccess={() => {
            refetch();
            setSelectedAppointment(null);
          }}
        />
      )}
    </Fragment>
  );
}
