import { FlashList } from "@shopify/flash-list";
import { router } from "expo-router";
import { sortBy } from "lodash/fp";
import { matchSorter } from "match-sorter";
import { useState } from "react";
import { View, SafeAreaView } from "react-native";
import { ClassCardSkeleton } from "~/components/modules/classes/card-skeleton";
import { EmptyState } from "~/components/modules/classes/reservations";
import { SearchInput } from "~/components/modules/classes/search-input";
import { StatChip } from "~/components/modules/sub/stat-chip";

import { SubCard } from "~/components/modules/sub/sub-card";
import { Button } from "~/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Text } from "~/components/ui/text";

import { useFetchSubs } from "~/modules/sub-management/queries/useFetchSubs";

import { MyRequestTab } from "~/components/modules/sub/my-tab";
import { format, parseISO } from "date-fns";

enum SubRequestTab {
  AllRequest = "request",
  ICanRequest = "I-can-request",
}

export default function SubManagement() {
  const { data, isPending, refetch } = useFetchSubs();

  const [tab, setTab] = useState(SubRequestTab.AllRequest);

  const [searchTerm, setSearchTerm] = useState("");

  const selectedData =
    tab === SubRequestTab.AllRequest
      ? data?.other_instructor_sub_requests
      : data?.pending_sub_requests;

  const sortedData = sortBy(
    [(item) => parseISO(item.request_date), "start_time", "class_name"],
    selectedData
  );

  const filteredData = searchTerm
    ? matchSorter(sortedData ?? [], searchTerm, {
        keys: [
          "class_name",
          "start_time",
          "gym_name",
          (item) => format(parseISO(item.request_date), "EEEE"), // day of week
          (item) => format(parseISO(item.request_date), "MMMM"), // month
          (item) => format(parseISO(item.request_date), "d"), // day of month
        ],
      })
    : sortedData;

  return (
    <SafeAreaView className="flex flex-1 mt-0">
      <View className="flex p-3 mt-2 flex-1 overflow-auto h-full">
        <View className="flex flex-row justify-between">
          <Text className="text-lg mt-2 font-extrabold text-[#002966] dark:text-white">
            SUB MANAGEMENT
          </Text>
          <Button
            onPress={() => router.push("/(classes)/(subs)/request")}
            className="bg-[#002966]"
            label="Request a sub"
          />
        </View>

        <View className="flex flex-row justify-between gap-2 mt-4">
          <View className="flex-1 mr-1">
            <StatChip
              label="Urgent Requests"
              count={data?.stats?.urgent_count}
            />
            <StatChip
              label="Waiting for Sub"
              count={data?.stats?.waiting_for_sub_count}
            />
          </View>
          <View className="flex-1 ml-1">
            <StatChip
              label="Total Staff Requests"
              count={data?.stats?.total_staff_requests_count}
            />
            <StatChip
              label="Approved Requests"
              count={data?.stats?.approved_requests_count}
            />
          </View>
        </View>

        <SearchInput
          className="mt-2"
          placeholder="Search by name, day, time or facility"
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          searchContext="sub_requests"
        />
        <Tabs
          onValueChange={(newValue) => setTab(newValue as SubRequestTab)}
          value={tab}
          className="w-full mx-auto flex-col gap-1.5 mb-2"
        >
          <TabsList className="flex-row w-full gap-2">
            <TabsTrigger
              label="Open Sub Request"
              value={SubRequestTab.AllRequest}
              className="flex-1 rounded-lg"
            />

            <TabsTrigger
              value={SubRequestTab.ICanRequest}
              className="flex-1 rounded-lg"
              label="Your Sub Requests"
            />
          </TabsList>
        </Tabs>

        {!filteredData?.length && !isPending ? (
          <EmptyState label="There is no data available" />
        ) : (
          <FlashList
            className="mt-4 w-full"
            data={filteredData}
            renderItem={({ item }) =>
              isPending ? (
                <ClassCardSkeleton />
              ) : tab === SubRequestTab.ICanRequest ? (
                <MyRequestTab {...item} />
              ) : (
                <SubCard {...item} />
              )
            }
            estimatedItemSize={80}
            onRefresh={refetch}
            refreshing={isPending}
          />
        )}
      </View>
    </SafeAreaView>
  );
}
