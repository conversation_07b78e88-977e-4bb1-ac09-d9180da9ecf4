import React, { Fragment, useState, use<PERSON>ffect, use<PERSON>emo } from "react";
import { TouchableOpacity, View } from "react-native";
import { ScreenTracker } from "~/components/analytics/ScreenTracker";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import { ClassCard } from "~/components/modules/classes/class-card";
import { matchSorter } from "match-sorter";
import { useClassesData } from "~/modules/classes/queries/useClassesQuery";
import { FlashList } from "@shopify/flash-list";
import { ClassCardSkeleton } from "~/components/modules/classes/card-skeleton";
import { format, parseISO } from "date-fns";

import SimpleLineIcons from "@expo/vector-icons/SimpleLineIcons";
import { SearchInput } from "~/components/modules/classes/search-input";
import { Text } from "~/components/ui/text";
import { useColorScheme } from "~/lib/useColorScheme";
import { WelcomeSection } from "~/components/modules/common/welcome";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "~/components/ui/tabs";
import { useAppointments } from "~/modules/appointments/queries/useAppointments";
import { useFetchSubs } from "~/modules/sub-management/queries/useFetchSubs";
import { HomeAppCard } from "~/components/modules/appointments/home-app-card";
import { HomeSubCard } from "~/components/modules/sub/home-sub-card";
import { EmptyState } from "~/components/modules/classes/reservations";
import { router } from "expo-router";
import { useHomeSubOptimisticUpdate } from "~/modules/sub-management/queries/useHomeSubOptimisticUpdate";

export const ClassEmptyState = ({ searchTerm }: { searchTerm?: string }) => {
  const { isDarkColorScheme } = useColorScheme();

  return (
    <View className="flex-1 items-center justify-center">
      <SimpleLineIcons
        name={searchTerm ? "magnifier" : "graduation"}
        size={40}
        color={isDarkColorScheme ? "white" : "#1A237E"}
        className="mb-4 opacity-50"
      />
      <Text className="text-xl font-bold text-[#1A237E] text-center dark:text-white">
        {searchTerm ? "No classes found" : "No classes scheduled"}
      </Text>
      <Text className="text-base text-gray-600 text-center mt-2">
        {searchTerm
          ? `We couldn't find any classes matching "${searchTerm}"`
          : "You have no classes scheduled for today"}
      </Text>
    </View>
  );
};

enum HomeTab {
  Classes = "today-classes",
  SubRequests = "sub-requests",
  Appointments = "today-appointments",
}

export default function Home() {
  const [activeTab, setActiveTab] = useState<HomeTab>(HomeTab.Classes);
  const [searchTerm, setSearchTerm] = useState("");
  const { trackEvent, EVENTS } = useAnalytics();
  const { refreshSubRequests } = useHomeSubOptimisticUpdate();

  const {
    data: classesData,
    isPending: isClassesPending,
    refetch: refetchClasses,
  } = useClassesData();

  const {
    data: appointmentsData,
    isPending: isAppointmentsPending,
    refetch: refetchAppointments,
  } = useAppointments(new Date());

  const {
    data: subsData,
    isPending: isSubsPending,
    refetch: refetchSubs,
  } = useFetchSubs();

  useEffect(() => {
    trackEvent(EVENTS.TAB_CHANGE, { tab_name: "Home" });
  }, []);

  const todayAppointments = useMemo(() => {
    if (!appointmentsData) return [];
    const today = format(new Date(), "yyyy-MM-dd");
    return appointmentsData.filter((appointment) => {
      const appointmentDate = format(
        parseISO(appointment.start_time),
        "yyyy-MM-dd"
      );
      return appointmentDate === today;
    });
  }, [appointmentsData]);

  const todaySubRequests = useMemo(() => {
    if (!subsData?.other_instructor_sub_requests) return [];
    // const today = format(new Date(), "yyyy-MM-dd");
    return subsData.other_instructor_sub_requests;
    // .filter((request) => {
    //   const requestDate = format(parseISO(request.request_date), "yyyy-MM-dd");
    //   return requestDate === today;
    // });
  }, [subsData]);

  // Filter data based on search term for active tab
  const filteredData = useMemo(() => {
    if (searchTerm) {
      switch (activeTab) {
        case HomeTab.Classes:
          return matchSorter(classesData ?? [], searchTerm, {
            keys: ["name", "start_time", "gym_name"],
          });
        case HomeTab.Appointments:
          return matchSorter(todayAppointments ?? [], searchTerm, {
            keys: [
              "equipment_name",
              "room_name",
              "first_name",
              "last_name",
              "gym_name",
            ],
          });
        case HomeTab.SubRequests:
          return matchSorter(todaySubRequests ?? [], searchTerm, {
            keys: ["class_name", "start_time", "gym_name"],
          });
        default:
          return [];
      }
    }

    switch (activeTab) {
      case HomeTab.Classes:
        return classesData ?? [];
      case HomeTab.Appointments:
        return todayAppointments ?? [];
      case HomeTab.SubRequests:
        return todaySubRequests ?? [];
      default:
        return [];
    }
  }, [activeTab, searchTerm, classesData, todayAppointments, todaySubRequests]);

  // Handle refresh based on active tab
  const handleRefresh = () => {
    switch (activeTab) {
      case HomeTab.Classes:
        refetchClasses();
        break;
      case HomeTab.Appointments:
        refetchAppointments();
        break;
      case HomeTab.SubRequests:
        // Use our optimistic update hook to force a refresh
        refreshSubRequests();
        // Also call the regular refetch function
        refetchSubs();
        break;
    }
  };

  // Determine if data is loading based on active tab
  const isLoading = useMemo(() => {
    switch (activeTab) {
      case HomeTab.Classes:
        return isClassesPending;
      case HomeTab.Appointments:
        return isAppointmentsPending;
      case HomeTab.SubRequests:
        return isSubsPending;
      default:
        return false;
    }
  }, [activeTab, isClassesPending, isAppointmentsPending, isSubsPending]);

  // Prepare display data with loading skeletons if needed
  const displayData = isLoading ? Array(5).fill({}) : filteredData;

  return (
    <ScreenTracker screenName="Home">
      <Fragment>
        <View className="p-6 flex flex-1" style={{ height: "100%" }}>
          <WelcomeSection salutation="HELLO" isCapitalized />

          <Tabs
            value={activeTab}
            onValueChange={(value) => {
              setActiveTab(value as HomeTab);
              setSearchTerm("");
            }}
            className="w-full mx-auto flex-col gap-1.5 mb-2 flex-1 mt-4"
          >
            <TabsList className="flex-row w-full gap-2 h-18 mb-2">
              <TabsTrigger
                value={HomeTab.Classes}
                className="flex-1 rounded-lg px-1 py-2"
                label="TODAY'S CLASSES"
              />
              <TabsTrigger
                value={HomeTab.SubRequests}
                className="flex-1 rounded-lg px-1 py-2"
                label="SUB REQUESTS"
              />
              <TabsTrigger
                value={HomeTab.Appointments}
                className="flex-1 rounded-lg px-1 py-2"
                label="TODAY'S APPTS"
                accessibilityLabel="TODAY'S APPOINTMENTS"
              />
            </TabsList>

            {/* Search Input */}
            <SearchInput
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              searchContext={`home_${activeTab}`}
              placeholder={"Search by name, time or facility"}
            />

            {/* Tab Content */}
            <TabsContent value={HomeTab.Classes} className="mt-2 flex-1">
              {!isLoading && (!displayData || !displayData?.length) ? (
                <ClassEmptyState searchTerm={searchTerm} />
              ) : (
                <View style={{ height: 500 }} className="flex-1">
                  <FlashList
                    className="w-full flex-1"
                    data={displayData}
                    renderItem={({ item }) =>
                      isLoading ? (
                        <ClassCardSkeleton />
                      ) : (
                        <ClassCard {...item} />
                      )
                    }
                    estimatedItemSize={80}
                    onRefresh={handleRefresh}
                    refreshing={isLoading}
                  />
                </View>
              )}
            </TabsContent>

            <TabsContent value={HomeTab.SubRequests} className="mt-2 flex-1">
              {!isLoading && (!displayData || !displayData?.length) ? (
                <EmptyState label="No sub requests for today" />
              ) : (
                <View style={{ height: 500 }} className="flex-1">
                  <FlashList
                    className="w-full flex-1"
                    data={displayData}
                    renderItem={({ item }) =>
                      isLoading ? (
                        <ClassCardSkeleton />
                      ) : (
                        <HomeSubCard {...item} />
                      )
                    }
                    estimatedItemSize={80}
                    onRefresh={handleRefresh}
                    refreshing={isLoading}
                  />
                </View>
              )}
            </TabsContent>

            <TabsContent value={HomeTab.Appointments} className="mt-2 flex-1">
              {!isLoading && (!displayData || !displayData?.length) ? (
                <EmptyState label="No appointments for today" />
              ) : (
                <View style={{ height: 500 }} className="flex-1">
                  <FlashList
                    className="w-full flex-1"
                    data={displayData}
                    renderItem={({ item }) =>
                      isLoading ? (
                        <ClassCardSkeleton />
                      ) : (
                        <TouchableOpacity
                          onPress={() =>
                            router.push({
                              pathname: "/(classes)/(appointment)/[id]",
                              params: {
                                ...item,
                              },
                            })
                          }
                        >
                          <HomeAppCard {...item} />
                        </TouchableOpacity>
                      )
                    }
                    estimatedItemSize={80}
                    onRefresh={handleRefresh}
                    refreshing={isLoading}
                  />
                </View>
              )}
            </TabsContent>
          </Tabs>
        </View>
      </Fragment>
    </ScreenTracker>
  );
}
