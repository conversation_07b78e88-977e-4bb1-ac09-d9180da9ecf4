import { FlashList } from "@shopify/flash-list";
import { format, startOfMonth, parseISO } from "date-fns";
import { matchSorter } from "match-sorter";
import { Fragment, useState, useMemo } from "react";
import { View } from "react-native";

import { ClassCardSkeleton } from "~/components/modules/classes/card-skeleton";
import { SearchInput } from "~/components/modules/classes/search-input";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";

import { EmptyState } from "~/components/modules/classes/reservations";
import { router } from "expo-router";
import { MonthDatePicker } from "~/components/modules/common/month-picker";
import { createSearchFields } from "~/utils/search";
import { useClassesSchedule } from "~/modules/classes/queries/useClassesSchedule";
import { ClassScheduleItem } from "~/modules/classes/types";

import { ClassRangeCard } from "~/components/modules/classes/class-range";

const searchFields = createSearchFields<ClassScheduleItem>([
  "name",
  "date",
  "gym_name",
  "subbing_instructor",
  "instructor_first_name",
  "instructor_last_name",
]);

const obtainEmptyState = (searchTerm: string, selectedDate?: Date) => {
  if (searchTerm) {
    return `No classes found for ${searchTerm}`;
  }
  if (selectedDate) {
    return `No classes found for ${format(selectedDate, "MMMM yyyy")}`;
  }
  return "You don't have any classes";
};

export default function Classes() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();

  const { data = [], isPending, refetch } = useClassesSchedule(selectedDate);

  const filteredData = useMemo(
    () =>
      searchTerm
        ? matchSorter(data, searchTerm, {
            keys: searchFields,
          })
        : data,
    [data, searchTerm]
  );

  return (
    <Fragment>
      <View className="p-6 flex flex-1">
        <SearchInput
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          placeholder="Search by name, day, time or facility"
          searchContext="classes_schedule"
        />

        <View className="flex flex-row justify-between items-center my-4 mr-2">
          <Text className="font-extrabold text-lg dark:text-orange-300 text-[#1A237E] ">
            {format(
              selectedDate ||
                (filteredData.length > 0
                  ? parseISO(filteredData[0].date)
                  : new Date()),
              "MMMM yyyy"
            ).toUpperCase()}
          </Text>
          <MonthDatePicker
            value={selectedDate ?? new Date()}
            modalTitle="Select Month & Year"
            onChange={(date) => {
              setSelectedDate(date);
              router.push({
                pathname: "/(classes)/(tabs)/classes",
                params: {
                  selectedDate: date
                    ? startOfMonth(date).toISOString()
                    : undefined,
                },
              });
            }}
          />
        </View>

        {isPending ? (
          <View className="mt-2 w-full">
            {Array.from({ length: 5 }).map((_, index) => (
              <ClassCardSkeleton key={index} />
            ))}
          </View>
        ) : !filteredData?.length ? (
          <EmptyState
            label={obtainEmptyState(searchTerm, selectedDate)}
            action={
              !searchTerm &&
              selectedDate && (
                <Button
                  className="bg-[#002966] mt-3"
                  onPress={() => setSelectedDate(undefined)}
                  label="Back to today"
                />
              )
            }
          />
        ) : (
          <FlashList
            className="mt-2 w-full"
            data={filteredData}
            renderItem={({ item }) => <ClassRangeCard {...item} />}
            estimatedItemSize={700}
            onRefresh={refetch}
            refreshing={isPending}
          />
        )}
      </View>
    </Fragment>
  );
}
