import { router, Stack } from "expo-router";

import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { useColorScheme } from "~/lib/useColorScheme";

export default function ContactClassesLayout() {
  const { isDarkColorScheme } = useColorScheme();

  return (
    <Stack>
      <Stack.Screen
        name="create"
        options={{
          title: "ADD CONTACT CLASS",
          headerTitleStyle: {
            color: isDarkColorScheme ? "white" : "#002966",
          },
          headerLeft: () => (
            <MaterialIcons
              name="arrow-back"
              size={20}
              color={isDarkColorScheme ? "white" : "#002966"}
              onPress={router.back}
            />
          ),
        }}
      />
    </Stack>
  );
}
