import React, { useState, useRef, useEffect } from "react";
import { View, ActivityIndicator, BackHandler } from "react-native";
import { Stack, useRouter } from "expo-router";
import { useColorScheme } from "~/lib/useColorScheme";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { WebView } from "react-native-webview";

export default function TermsAndConditionsScreen() {
  const router = useRouter();
  const { isDarkColorScheme } = useColorScheme();
  const [loading, setLoading] = useState(true);
  const webViewRef = useRef(null);

  // Handle back button press to navigate within WebView if possible
  useEffect(() => {
    const backAction = () => {
      if (webViewRef.current) {
        // @ts-ignore - TypeScript doesn't know about goBack method
        webViewRef.current.goBack();
        return true; // Prevent default behavior
      }
      return false; // Let the default behavior happen
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );

    return () => backHandler.remove();
  }, []);

  return (
    <View style={{ flex: 1 }}>
      <Stack.Screen
        options={{
          title: "Terms & Conditions",
          headerLeft: () => (
            <MaterialIcons
              name="arrow-back"
              size={20}
              color={isDarkColorScheme ? "white" : "black"}
              onPress={() => router.back()}
              style={{ marginLeft: 10 }}
            />
          ),
        }}
      />

      {loading && (
        <View
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: isDarkColorScheme ? "#121212" : "#FFFFFF",
            zIndex: 10,
          }}
        >
          <ActivityIndicator size="large" color="#069CC3" />
        </View>
      )}

      <WebView
        ref={webViewRef}
        source={{
          uri: "https://rachel-koretsky.squarespace.com/terms-and-privacy-new",
        }}
        style={{ flex: 1 }}
        onLoadStart={() => setLoading(true)}
        onLoad={() => setLoading(false)}
        onError={() => setLoading(false)}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={true}
        scalesPageToFit={true}
      />
    </View>
  );
}
