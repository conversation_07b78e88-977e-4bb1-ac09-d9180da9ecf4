import {
  BarcodeScanningResult,
  CameraView,
  useCameraPermissions,
} from "expo-camera";

import { StyleSheet, Dimensions, AppState } from "react-native";

import { Canvas, DiffRect, rect, rrect } from "@shopify/react-native-skia";

import { Fragment, useEffect, useRef } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { Button } from "./ui/button";

const { width, height } = Dimensions.get("window");

const innerDimension = width / 1.2;

const outer = rrect(rect(0, 0, width, height), 0, 0);
const inner = rrect(
  rect(
    width / 2 - innerDimension / 2,
    height / 2.5 - innerDimension / 2,
    innerDimension,
    innerDimension
  ),
  10,
  10
);

export const Overlay = () => {
  return (
    <Canvas style={StyleSheet.absoluteFillObject}>
      <DiffRect inner={inner} outer={outer} color="black" opacity={0.5} />
    </Canvas>
  );
};

export function Camera({
  onBarcodeScanned,
}: {
  onBarcodeScanned: (data: BarcodeScanningResult["data"]) => void;
}) {
  const qrLock = useRef(false);
  const appState = useRef(AppState.currentState);

  const [permission, requestPermission] = useCameraPermissions();

  const isPermissionGranted = Boolean(permission?.granted);

  useEffect(() => {
    const subscription = AppState.addEventListener("change", (nextAppState) => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === "active"
      ) {
        qrLock.current = false;
      }
      appState.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, []);

  return (
    <SafeAreaView
      className="flex-1 justify-center items-center"
      style={StyleSheet.absoluteFillObject}
    >
      <Button
        onPress={requestPermission}
        className="text-center items-center p-2"
        label="Allow Camera Access"
      />

      {isPermissionGranted ? (
        <Fragment>
          <CameraView
            barcodeScannerSettings={{
              barcodeTypes: [
                "aztec",
                "ean13",
                "ean8",
                "qr",
                "pdf417",
                "upc_e",
                "datamatrix",
                "code39",
                "code93",
                "itf14",
                "codabar",
                "code128",
                "upc_a",
              ],
            }}
            style={StyleSheet.absoluteFillObject}
            facing="back"
            onBarcodeScanned={({ data }) => {
              if (data && !qrLock.current) {
                qrLock.current = true;
                setTimeout(async () => {
                  await onBarcodeScanned(data);
                }, 500);
              }
            }}
          />
          <Overlay />
        </Fragment>
      ) : null}
    </SafeAreaView>
  );
}
