import React, { useEffect } from "react";
import { useAnalytics } from "~/modules/hooks/useAnalytics";

interface ScreenTrackerProps {
  screenName: string;
  screenClass?: string;
  params?: Record<string, any>;
  children: React.ReactNode;
}

/**
 * Component to track screen views in Firebase Analytics
 * Wrap your screen components with this to automatically track screen views
 */
export const ScreenTracker: React.FC<ScreenTrackerProps> = ({
  screenName,
  screenClass,
  params,
  children,
}) => {
  const { trackScreenView } = useAnalytics();

  useEffect(() => {
    // Track screen view when component mounts
    trackScreenView(screenName, screenClass);
  }, [screenName, screenClass, trackScreenView]);

  return <>{children}</>;
};
