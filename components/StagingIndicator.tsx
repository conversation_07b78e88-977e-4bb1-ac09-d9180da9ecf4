import { Text } from "react-native";
import { isStaging } from "~/lib/utils";
import { useColorScheme } from "~/lib/useColorScheme";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "~/components/ui/tooltip";

export function StagingIndicator() {
  const { isDarkColorScheme } = useColorScheme();

  if (!isStaging()) return null;

  return (
    <Tooltip>
      <TooltipTrigger>
        <MaterialIcons
          name="layers"
          size={20}
          color={isDarkColorScheme ? "#fbbf24" : "#eab308"}
          style={{ marginLeft: 8, marginTop: 2 }}
        />
      </TooltipTrigger>
      <TooltipContent side="bottom">
        <Text className="text-muted-foreground">Env: Staging</Text>
        <Text className="text-muted-foreground">Using staging data</Text>
      </TooltipContent>
    </Tooltip>
  );
}
