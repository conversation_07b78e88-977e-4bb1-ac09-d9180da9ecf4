import React, { Fragment, useState } from "react";
import { View } from "react-native";
import {
  <PERSON><PERSON>,
  <PERSON>alogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "~/components/ui/dialog";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { DatePicker } from "~/components/modules/common/date-picker";
import { formatClassDate } from "~/modules/classes/utils";
import { useUpdateReservation } from "~/modules/classes/mutations/useUpdateReservation";
import { parseISO } from "date-fns";
import { useTrainerData } from "~/modules/appointments/queries/useTrainerData";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form, FormField, FormMessage } from "~/components/ui/form";
import { ComboBox } from "../common/Combo-box";
import { AutocompleteDropdownContextProvider } from "react-native-autocomplete-dropdown";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import { getCalendarEventId } from "~/modules/calendar/calendar-storage";
import { updateCalendarEvent } from "~/modules/calendar/calendar-utils";
import { showSuccessToast, showErrorToast } from "~/components/toast";

// Define the form schema
const EditReservationSchema = z.object({
  date: z.date({
    required_error: "Date is required",
  }),
  time: z.object(
    {
      id: z.string(),
      title: z.string(),
    },
    { required_error: "Time is required" }
  ),
});

type EditReservationFormValues = z.infer<typeof EditReservationSchema>;

interface SimpleEditModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  reservationId: number;
  currentDate: string;
  onSuccess?: () => void;
  equipment_id?: number;
  userId?: number;
}

export function AppointmentEditModal({
  isOpen,
  setIsOpen,
  reservationId,
  currentDate,
  onSuccess,
  equipment_id,
  userId,
}: SimpleEditModalProps) {
  const { trackEvent, EVENTS } = useAnalytics();
  // Parse the current date
  const parsedDate = parseISO(currentDate);

  // Setup form with React Hook Form
  const form = useForm<EditReservationFormValues>({
    resolver: zodResolver(EditReservationSchema),
    defaultValues: {
      date: parsedDate,
    },
  });

  // Get the selected date from the form
  const selectedDate = form.watch("date") || parsedDate;
  const [selectedTime, setSelectedTime] = useState<{
    id: string;
    title: string;
  } | null>(null);

  // Get available time blocks for the selected date
  const { data, isPending } = useTrainerData({
    date: formatClassDate(selectedDate),
    equipment_id: String(equipment_id),
  });

  // Update reservation mutation
  const { mutate: updateReservation, isPending: isUpdating } =
    useUpdateReservation(async () => {
      // Handle calendar event update after successful reservation update
      try {
        // Get the calendar event ID for this appointment
        const calendarEventId = await getCalendarEventId(reservationId);

        if (calendarEventId) {
          const formValues = form.getValues();

          // Parse the new start time
          const [startHour, startMinute] = selectedTime!.id
            .split(":")
            .map(Number);

          // Create Date objects for start and end times
          const startDate = new Date(formValues.date);
          startDate.setHours(startHour, startMinute, 0, 0);

          // End time is 1 hour after start time
          const endDate = new Date(startDate);
          endDate.setHours(startDate.getHours() + 1);

          // Update the calendar event
          const updated = await updateCalendarEvent(
            calendarEventId,
            `Appointment with Member`, // You might want to get the actual member name
            startDate.toISOString(),
            endDate.toISOString(),
            "", // Location - you might want to get the actual equipment/room name
            "Personal training appointment"
          );

          if (updated) {
            showSuccessToast("Appointment and calendar event updated");
          } else {
            showErrorToast(
              "Appointment updated, but calendar event could not be updated"
            );
          }
        } else {
          showSuccessToast("Appointment updated");
        }
      } catch (error) {
        console.error("Error updating calendar event:", error);
        showErrorToast(
          "Appointment updated, but calendar event could not be updated"
        );
      }

      setIsOpen(false);
      if (onSuccess) onSuccess();
    });

  const handleUpdate = () => {
    if (!selectedTime) return;

    form.setValue("time", selectedTime);
    const formValues = form.getValues();

    const newStartTime = `${formatClassDate(formValues.date)} ${
      formValues.time.id
    }`;

    updateReservation({
      user_id: userId as number,
      reservation_id: reservationId,
      equipment_id: String(equipment_id),
      type: "pt",
      start_time: newStartTime,
    });

    // Track appointment edit event
    trackEvent(EVENTS.EDIT_APPOINTMENT, {
      reservation_id: reservationId,
      user_id: userId,
      equipment_id: equipment_id,
      new_date: formatClassDate(formValues.date),
      new_time: formValues.time.title,
      old_date: currentDate,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent
        className="w-[85vw] mx-auto my-auto h-auto max-h-[400px] min-h-[320px] rounded-xl shadow-lg p-4"
        accessibilityViewIsModal={true}
        accessibilityLabel="Edit reservation dialog"
      >
        <DialogHeader className="pb-1">
          <DialogTitle
            className="text-center"
            accessibilityRole="header"
            accessibilityLabel="Edit Reservation"
          >
            Edit Reservation
          </DialogTitle>
          <DialogDescription
            className="text-center text-sm"
            accessibilityLabel="Edit the date and time for this reservation"
          >
            Edit the date and time
          </DialogDescription>
        </DialogHeader>

        <AutocompleteDropdownContextProvider>
          <Form {...form}>
            <View className="space-y-4">
              <FormField
                control={form.control}
                name="date"
                render={({ field }) => (
                  <View>
                    <DatePicker
                      label="Date"
                      value={field.value}
                      onChange={(date) => {
                        field.onChange(date);
                        setSelectedTime(null);
                      }}
                    />
                    <FormMessage />
                  </View>
                )}
              />

              <View>
                <Text className="text-sm font-medium">Time</Text>
                <View
                  style={{
                    borderRadius: 5,
                    height: 50,
                    marginBottom: 10,
                  }}
                >
                  <ComboBox
                    isLoading={isPending}
                    direction="up"
                    data={data?.availableTimeBlocks || []}
                    placeholder="Select time"
                    onSelect={(item) => {
                      if (item) {
                        setSelectedTime({
                          id: item.id,
                          title: item.title || "",
                        });
                      }
                    }}
                    value={selectedTime?.title ?? ""}
                    emptyText="No available time block to select"
                  />
                </View>
              </View>
            </View>

            <DialogFooter className="flex flex-row mt-4 gap-2">
              <Button
                onPress={handleUpdate}
                disabled={isUpdating || !selectedTime}
                isLoading={isUpdating}
                label="Update"
                className="flex-1 py-1"
              />

              <DialogClose asChild>
                <Button
                  label="Cancel"
                  variant={"secondary"}
                  className="flex-1 py-1"
                />
              </DialogClose>
            </DialogFooter>
          </Form>
        </AutocompleteDropdownContextProvider>
      </DialogContent>
    </Dialog>
  );
}
