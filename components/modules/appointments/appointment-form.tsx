import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import * as React from "react";
import { useForm, useWatch } from "react-hook-form";
import { ScrollView, View } from "react-native";
import * as z from "zod";
import { Button } from "~/components/ui/button";
import { Form, FormField, FormLabel, FormMessage } from "~/components/ui/form";

import { Text } from "~/components/ui/text";

import { ComboBox } from "~/components/modules/common/Combo-box";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";

import { DatePicker } from "~/components/modules/common/date-picker";
import { RecurringEndDatePicker } from "~/components/modules/common/recurring-end-date-picker";

import { CheckBoxBase } from "~/components/ui/checkbox";
import { router } from "expo-router";

import { SearchUsers } from "../../../modules/search-users";
import { useTrainerData } from "../../../modules/appointments/queries/useTrainerData";
import { formatClassDate } from "../../../modules/classes/utils";
import { AutocompleteDropdownContextProvider } from "react-native-autocomplete-dropdown";
import { useUserMembership } from "../../../modules/classes/queries/useUserMembership";
import { addDays, sub } from "date-fns";

export const AppointmentSchema = z
  .object({
    date: z.date({
      message: "Date is required",
    }),
    user: z.object(
      {
        id: z.number(),
        title: z.string(),
      },
      { message: "Member is required" }
    ),
    equipment: z.object(
      {
        id: z.string(),
        title: z.string(),
      },
      { message: "Equipment is required" }
    ),
    membership: z
      .object(
        {
          id: z.string(),
          title: z.string(),
        },
        { message: "Membership is required" }
      )
      .nullish(),
    startTime: z.object(
      {
        id: z.string().min(1),
        title: z.string().min(1),
      },
      { message: "Start time is required" }
    ),
    equipmentType: z
      .object({
        id: z.string(),
        title: z.string(),
      })
      .nullish(),
    checkInReservation: z.boolean().default(false),
    recurring: z.boolean().default(false),
    recurringEndDate: z.date().optional(),
    recurringDays: z
      .object({
        monday: z.boolean().default(false),
        tuesday: z.boolean().default(false),
        wednesday: z.boolean().default(false),
        thursday: z.boolean().default(false),
        friday: z.boolean().default(false),
        saturday: z.boolean().default(false),
        sunday: z.boolean().default(false),
      })
      .optional(),
  })
  .superRefine((data, ctx) => {
    // If recurring is true, at least one day must be selected
    if (data.recurring) {
      // If recurringDays is undefined or null, validation should fail
      if (!data.recurringDays) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Please select at least one day for recurring appointments",
          path: ["recurringDays"],
        });
        return;
      }

      // Check if at least one day is selected
      const hasSelectedDay = Object.values(data.recurringDays).some(
        (day) => day === true
      );
      if (!hasSelectedDay) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Please select at least one day for recurring appointments",
          path: ["recurringDays"],
        });
      }
    }
  });

export const AddAppointmentForm = ({
  onSubmit,
  isLoading,
  equipmentData,
  form,
}: {
  onSubmit: (data: z.infer<typeof AppointmentSchema>) => void;
  isLoading?: boolean;
  equipmentData?: { id: string; title: string }[];
  form: ReturnType<typeof useForm<z.infer<typeof AppointmentSchema>>>;
}) => {
  const selectedValues = useWatch({
    control: form.control,
  });

  const { data, isPending } = useTrainerData({
    date: formatClassDate(selectedValues.date),
    equipment_id: selectedValues.equipment?.id ?? "",
  });

  const { data: memberShipData = [] } = useUserMembership({
    itemId: selectedValues.equipment?.id as string,
    userId: selectedValues.user?.id as number,
    type: "pt",
  });

  const resetStartTime = () => form.resetField("startTime");

  const resetMembership = () => form.resetField("membership");

  const membershipOptions = React.useMemo(
    () =>
      memberShipData.map((rec) => ({
        ...rec,
        id: String(rec.id),
        title: rec.display_text,
      })),
    [memberShipData]
  );

  // Ensure recurringDays is initialized when recurring is checked
  React.useEffect(() => {
    if (selectedValues.recurring && !selectedValues.recurringDays) {
      form.setValue("recurringDays", {
        monday: false,
        tuesday: false,
        wednesday: false,
        thursday: false,
        friday: false,
        saturday: false,
        sunday: false,
      });
    }
  }, [selectedValues.recurring, selectedValues.recurringDays, form]);

  return (
    <AutocompleteDropdownContextProvider>
      <View className="flex-1">
        <Form {...form}>
          <View className="flex-1">
            <KeyboardAwareScrollView
              bottomOffset={10}
              contentContainerStyle={{
                paddingHorizontal: 16,
                paddingTop: 16,
                paddingBottom: 100,
              }}
              className="flex-1"
            >
              <ScrollView className="space-y-4">
                <View className="space-y-4">
                  <FormField
                    control={form.control}
                    name="date"
                    render={({ field }) => (
                      <View className="space-y-3">
                        <DatePicker
                          value={field.value}
                          onChange={(date) => {
                            field.onChange(date);
                            resetStartTime();
                            resetMembership();
                          }}
                          label="Date*"
                          minDate={sub(new Date(), { months: 6 })}
                        />
                        <FormMessage />
                      </View>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="equipment"
                    render={({ field }) => {
                      const value = field.value;
                      return (
                        <View className="space-y-3">
                          <FormLabel>Appointment Type*</FormLabel>
                          <View className="h-14">
                            <ComboBox
                              isLoading={isPending}
                              direction="up"
                              data={data?.trainingSessions ?? []}
                              placeholder="Select Appointment Type"
                              initialValue={value?.id ?? ""}
                              value={value?.title ?? ""}
                              onSelect={(val) => {
                                field.onChange(val);
                                resetStartTime();
                                resetMembership();
                              }}
                            />
                          </View>
                          <FormMessage />
                        </View>
                      );
                    }}
                  />

                  <View className="mt-2">
                    <FormField
                      control={form.control}
                      name="user"
                      render={({ field }) => (
                        <View className="space-y-3">
                          <FormLabel>Member*</FormLabel>

                          <SearchUsers
                            position="up"
                            onSelect={(val) => {
                              field.onChange(val);
                              resetMembership();
                            }}
                          />

                          <FormMessage />
                        </View>
                      )}
                    />
                  </View>

                  <View className="mt-4">
                    <FormField
                      control={form.control}
                      name="membership"
                      render={({ field }) => {
                        const value = field.value;
                        return (
                          <View className="space-y-3">
                            <FormLabel>Membership Type (Optional)</FormLabel>
                            <View className="flex h-14">
                              <ComboBox
                                isLoading={isPending}
                                direction="up"
                                data={membershipOptions}
                                placeholder="Search Membership Type"
                                initialValue={value?.id ?? ""}
                                value={value?.title ?? ""}
                                onSelect={field.onChange}
                                emptyText="No available membership to select"
                              />
                            </View>
                            <FormMessage />
                          </View>
                        );
                      }}
                    />
                  </View>

                  <View className="mt-4">
                    <FormField
                      control={form.control}
                      name="startTime"
                      render={({ field }) => {
                        const value = field.value;
                        return (
                          <View className="space-y-3">
                            <FormLabel>Time*</FormLabel>
                            <View className="flex h-14">
                              <ComboBox
                                isLoading={isPending}
                                direction="up"
                                data={data?.availableTimeBlocks ?? []}
                                placeholder="Select start time"
                                onSelect={field.onChange}
                                initialValue={value?.id ?? ""}
                                value={value?.title ?? ""}
                                emptyText="No available time block to select"
                              />
                            </View>
                            <FormMessage className="text-red-500" />
                          </View>
                        );
                      }}
                    />
                  </View>

                  <View className="mt-4">
                    <FormField
                      control={form.control}
                      name="equipmentType"
                      render={({ field }) => {
                        const value = field.value;
                        return (
                          <View className="space-y-3">
                            <FormLabel>Equipment / Court (Optional)</FormLabel>
                            <View className="flex h-14">
                              <ComboBox
                                direction="up"
                                data={equipmentData || []}
                                placeholder="Select equipment"
                                onSelect={field.onChange}
                                initialValue={value?.id ?? ""}
                                value={value?.title ?? ""}
                                emptyText="No available equipment to select"
                              />
                            </View>
                            <FormMessage />
                          </View>
                        );
                      }}
                    />
                  </View>

                  <View className="mt-4">
                    <FormField
                      control={form.control}
                      name="recurring"
                      render={({ field: { value, onChange } }) => (
                        <View className="space-y-3">
                          <View className="flex-row items-center">
                            <CheckBoxBase
                              setChecked={(checked) => {
                                onChange(checked);

                                // When recurring is checked, ensure recurringDays is initialized
                                if (
                                  checked &&
                                  !form.getValues().recurringDays
                                ) {
                                  form.setValue("recurringDays", {
                                    monday: false,
                                    tuesday: false,
                                    wednesday: false,
                                    thursday: false,
                                    friday: false,
                                    saturday: false,
                                    sunday: false,
                                  });
                                }
                              }}
                              checked={Boolean(value)}
                              label="Recurring Appointment"
                            />
                          </View>
                          <Text className="text-gray-600 text-sm pl-7 dark:text-gray-400">
                            Create recurring appointments based on selected
                            days.
                          </Text>
                          <FormMessage />
                        </View>
                      )}
                    />
                  </View>

                  {selectedValues.recurring && (
                    <View className="mt-4 dark:bg-gray-800 p-4 rounded-lg">
                      <Text className="font-medium mb-3">
                        Select which days this reservation should recur *
                      </Text>

                      <View className="mb-4">
                        <Text className="text-sm font-medium mb-2">
                          Select Days *
                        </Text>
                        <View
                          className="flex-row flex-wrap justify-between"
                          style={{ width: "100%" }}
                        >
                          <FormField
                            control={form.control}
                            name="recurringDays.monday"
                            render={({ field: { value, onChange } }) => (
                              <View
                                className="mr-4 mb-2"
                                style={{ minWidth: 100 }}
                              >
                                <CheckBoxBase
                                  setChecked={onChange}
                                  checked={Boolean(value)}
                                  label="Monday"
                                />
                              </View>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="recurringDays.tuesday"
                            render={({ field: { value, onChange } }) => (
                              <View className="mr-4 mb-2">
                                <CheckBoxBase
                                  setChecked={onChange}
                                  checked={Boolean(value)}
                                  label="Tuesday"
                                />
                              </View>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="recurringDays.wednesday"
                            render={({ field: { value, onChange } }) => (
                              <View className="mr-4 mb-2">
                                <CheckBoxBase
                                  setChecked={onChange}
                                  checked={Boolean(value)}
                                  label="Wednesday"
                                />
                              </View>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="recurringDays.thursday"
                            render={({ field: { value, onChange } }) => (
                              <View className="mr-4 mb-2">
                                <CheckBoxBase
                                  setChecked={onChange}
                                  checked={Boolean(value)}
                                  label="Thursday"
                                />
                              </View>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="recurringDays.friday"
                            render={({ field: { value, onChange } }) => (
                              <View className="mr-4 mb-2">
                                <CheckBoxBase
                                  setChecked={onChange}
                                  checked={Boolean(value)}
                                  label="Friday"
                                />
                              </View>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="recurringDays.saturday"
                            render={({ field: { value, onChange } }) => (
                              <View className="mr-4 mb-2">
                                <CheckBoxBase
                                  setChecked={onChange}
                                  checked={Boolean(value)}
                                  label="Saturday"
                                />
                              </View>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="recurringDays.sunday"
                            render={({ field: { value, onChange } }) => (
                              <View className="mr-4 mb-2">
                                <CheckBoxBase
                                  setChecked={onChange}
                                  checked={Boolean(value)}
                                  label="Sunday"
                                />
                              </View>
                            )}
                          />
                        </View>
                        {selectedValues.recurring && (
                          <Text className="text-red-500 text-sm font-medium mt-2">
                            {form.formState.errors.recurringDays
                              ? "Please select at least one day for recurring appointments"
                              : ""}
                          </Text>
                        )}
                      </View>

                      <View>
                        <FormField
                          control={form.control}
                          name="recurringEndDate"
                          render={({ field }) => (
                            <View className="space-y-3">
                              <RecurringEndDatePicker
                                value={field.value || addDays(new Date(), 7)}
                                onChange={(date) => {
                                  field.onChange(date);
                                }}
                                label="End Date (Max 3 Months)"
                                startDate={selectedValues.date || new Date()}
                              />
                              <FormMessage />
                            </View>
                          )}
                        />
                      </View>
                    </View>
                  )}

                  <View className="mt-4 mb-8">
                    <FormField
                      control={form.control}
                      name="checkInReservation"
                      render={({ field: { value, onChange } }) => {
                        // Check if membership is selected
                        const hasMembership = Boolean(
                          selectedValues.membership
                        );

                        return (
                          <View className="space-y-3">
                            <View className="flex-row items-center">
                              <CheckBoxBase
                                setChecked={(checked) => {
                                  // Only allow changes if membership is selected
                                  if (hasMembership) {
                                    onChange(checked);
                                  }
                                }}
                                checked={Boolean(value)}
                                label="Check in Reservation"
                                // Pass disabled prop when no membership is selected
                                disabled={!hasMembership}
                              />
                            </View>
                            <Text className="text-gray-600 text-sm pl-7 dark:text-gray-400">
                              {hasMembership
                                ? "Check in reservation immediately after creation."
                                : "Select a membership to enable check-in."}
                            </Text>
                            <FormMessage />
                          </View>
                        );
                      }}
                    />
                  </View>
                </View>
              </ScrollView>
            </KeyboardAwareScrollView>

            {/* Fixed buttons at the bottom */}
            <View className="absolute bottom-0 left-0 right-0 bg-white p-4 border-t border-gray-200">
              <View className="space-y-3">
                <Button
                  disabled={isLoading}
                  onPress={form.handleSubmit(onSubmit)}
                  className="w-full mb-4"
                  isLoading={isLoading}
                  label={
                    selectedValues.recurring
                      ? "Add Recurring Reservations"
                      : "Add Reservation"
                  }
                />

                <Button
                  label="Close"
                  className="w-full bg-gray-400"
                  onPress={router.back}
                />
              </View>
            </View>
          </View>
        </Form>
      </View>
    </AutocompleteDropdownContextProvider>
  );
};
