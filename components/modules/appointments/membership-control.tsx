import { useState } from "react";
import { TouchableOpacity } from "react-native";
import { Switch } from "~/components/ui/switch";
import { MembershipAssignModal } from "./membership-assign-modal";
import { useCheckInMutation } from "~/modules/classes/mutations/useCheckInMutation";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { useColorScheme } from "~/lib/useColorScheme";

interface MembershipControlProps {
  id: number;
  isCheckIn: boolean;
  membership_id?: string | null;
  equipment_id?: number | string;
  user_id?: number;
  onRefresh?: () => void;
  date?: string;
}

export function MembershipControl({
  id,
  isCheckIn,
  membership_id,
  equipment_id,
  date,
  user_id,
  onRefresh,
}: MembershipControlProps) {
  const { mutate: handleCheckIn, isPending } = useCheckInMutation();
  const [checkIn, setCheckIn] = useState(isCheckIn);
  const [openMembershipModal, setOpenMembershipModal] = useState(false);

  const handleCheckInToggle = () => {
    setCheckIn(!checkIn);
    handleCheckIn({
      id,
      isCheckIn: !isCheckIn,
    });
  };

  const isChecked = isCheckIn ?? checkIn;
  const hasMembership = Boolean(membership_id);

  const { isDarkColorScheme } = useColorScheme();

  return (
    <>
      {hasMembership ? (
        <Switch
          checked={isChecked}
          onCheckedChange={handleCheckInToggle}
          isLoading={isPending}
          accessibilityLabel={
            isChecked ? "Check-in enabled" : "Check-in disabled"
          }
          accessibilityRole="switch"
          accessibilityHint="Toggle to check in or check out the member for this appointment"
        />
      ) : (
        <TouchableOpacity
          onPress={() => setOpenMembershipModal(true)}
          accessibilityLabel="Assign membership"
          accessibilityRole="button"
          accessibilityHint="Opens dialog to assign a membership to this member for the appointment"
        >
          <MaterialIcons
            name="assignment-add"
            size={25}
            color={isDarkColorScheme ? "white" : "#1A237E"}
          />
        </TouchableOpacity>
      )}

      {openMembershipModal && equipment_id && user_id && (
        <MembershipAssignModal
          isOpen={openMembershipModal}
          setIsOpen={setOpenMembershipModal}
          reservationId={id}
          userId={user_id}
          equipmentId={equipment_id}
          onSuccess={onRefresh}
          date={date}
        />
      )}
    </>
  );
}
