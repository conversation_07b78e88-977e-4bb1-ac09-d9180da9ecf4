import { View } from "react-native";
import { Card, CardContent } from "~/components/ui/card";
import { Text } from "~/components/ui/text";
import { getInitials } from "~/modules/classes/utils";
import { Fragment, ReactNode } from "react";
import { BaseAvatar } from "../classes/avatar";
import { MembershipControl } from "./membership-control";
import { CancelControl } from "./cancel-control";

import Ionicons from "@expo/vector-icons/Ionicons";

export function ApptUser({
  name,
  isCheckIn,
  id,
  image,
  extraActions,
  date,
  membership_id,
  equipment_id,
  user_id,
  onRefresh,
  isDecremented,
}: {
  name: string;
  isCheckIn: boolean;
  date?: string;
  id: number;
  image?: string;
  extraActions?: ReactNode;
  membership_id?: string | null;
  equipment_id?: number | string;
  user_id?: number;
  onRefresh?: () => void;
  isDecremented?: boolean;
}) {
  return (
    <Fragment>
      <Card className="w-full mt-4">
        <CardContent className="p-0 flex flex-row justify-between">
          <View className="flex flex-row gap-2 p-3">
            <BaseAvatar url={image ?? ""} name={getInitials(name)} />
            <Text className="font-bold mt-2">{name}</Text>
          </View>
          {isDecremented ? (
            <View className="flex flex-row items-center gap-4 pr-3">
              <View className="flex flex-row gap-1">
                <Ionicons name="checkmark-done" size={15} color="#22c55e" />
                <Text className="text-xs text-green-500  font-bold items-center">
                  Checked in
                </Text>
              </View>

              <View className="flex flex-row gap-1">
                <Ionicons name="checkmark-done" size={15} color="#3b82f6" />
                <Text className="text-xs text-blue-500  font-bold items-center">
                  Decremented
                </Text>
              </View>
            </View>
          ) : (
            <View className="flex flex-row items-center gap-4">
              <MembershipControl
                id={id}
                isCheckIn={isCheckIn}
                membership_id={membership_id}
                equipment_id={equipment_id}
                user_id={user_id}
                onRefresh={onRefresh}
                date={date}
              />
              {extraActions}
              <CancelControl id={id} isChecked={isCheckIn} type="appointment" />
            </View>
          )}
        </CardContent>
      </Card>
    </Fragment>
  );
}
