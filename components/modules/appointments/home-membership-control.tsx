import { useState } from "react";
import { TouchableOpacity } from "react-native";
import { Switch } from "~/components/ui/switch";
import { MembershipAssignModal } from "./membership-assign-modal";
import { useCheckInMutation } from "~/modules/classes/mutations/useCheckInMutation";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { useColorScheme } from "~/lib/useColorScheme";
import { useHomeOptimisticUpdate } from "~/modules/appointments/queries/useHomeOptimisticUpdate";

interface HomeMembershipControlProps {
  id: number;
  isCheckIn: boolean;
  membership_id?: string | null;
  equipment_id?: number | string;
  user_id?: number;
  onRefresh?: () => void;
  date?: string;
}

export function HomeMembershipControl({
  id,
  isCheckIn,
  membership_id,
  equipment_id,
  date,
  user_id,
  onRefresh,
}: HomeMembershipControlProps) {
  // Use a custom success callback that combines the onRefresh prop with our optimistic update
  const { updateAppointmentCheckIn } = useHomeOptimisticUpdate();

  const handleSuccess = () => {
    // Call the onRefresh prop if provided
    if (onRefresh) {
      onRefresh();
    }
  };

  const { mutate: handleCheckIn, isPending } =
    useCheckInMutation(handleSuccess);

  // Initialize local state with the prop value
  const [checkIn, setCheckIn] = useState(isCheckIn);
  const [openMembershipModal, setOpenMembershipModal] = useState(false);

  const handleCheckInToggle = () => {
    const newCheckInState = !checkIn;
    setCheckIn(newCheckInState);

    // Optimistically update the UI immediately
    updateAppointmentCheckIn(id, newCheckInState);

    // Make the API call
    handleCheckIn({
      id,
      isCheckIn: newCheckInState,
      type: "pt",
    });
  };

  // Use the actual state from props for initial render, but then use local state for updates
  // This ensures we show the correct state initially but can update it optimistically
  const isChecked = checkIn;
  const hasMembership = Boolean(membership_id);

  const { isDarkColorScheme } = useColorScheme();

  return (
    <>
      {hasMembership ? (
        <Switch
          checked={isChecked}
          onCheckedChange={handleCheckInToggle}
          isLoading={isPending}
        />
      ) : (
        <TouchableOpacity onPress={() => setOpenMembershipModal(true)}>
          <MaterialIcons
            name="assignment-add"
            size={25}
            color={isDarkColorScheme ? "white" : "#1A237E"}
          />
        </TouchableOpacity>
      )}

      {openMembershipModal && equipment_id && user_id && (
        <MembershipAssignModal
          isOpen={openMembershipModal}
          setIsOpen={setOpenMembershipModal}
          reservationId={id}
          userId={user_id}
          equipmentId={equipment_id}
          onSuccess={onRefresh}
          date={date}
        />
      )}
    </>
  );
}
