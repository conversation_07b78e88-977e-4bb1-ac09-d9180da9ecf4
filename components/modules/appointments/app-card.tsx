import { format, parseISO } from "date-fns";
import { View } from "react-native";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
} from "~/components/ui/card";
import { Text } from "~/components/ui/text";
import { Appointment } from "~/modules/appointments/types";
import { obtainDateFrame } from "~/modules/classes/utils";
import { ApptUser } from "./appt-user";
import { DATE_FORMAT } from "~/constants/date-formats";
import { ReactNode } from "react";

interface AppCardProps extends Appointment {
  extraActions?: ReactNode;
  topRightContent?: ReactNode;
  onRefresh?: () => void;
  renderUser?: (props: {
    id: number;
    name: string;
    isCheckIn: boolean;
    image?: string;
    membership_id?: string | null;
    equipment_id?: number | string;
    user_id?: number;
    onRefresh?: () => void;
    extraActions?: ReactNode;
  }) => ReactNode;
}

export function AppCard({
  equipment_name,
  start_time,
  end_time,
  room_name,
  last_name,
  first_name,
  id,
  checkin,
  image,
  membership_id,
  equipment_id,
  user_id,
  extraActions,
  topRightContent,
  onRefresh,
  renderUser,
  decrements_sync_id,
}: AppCardProps) {
  const formattedDate = format(parseISO(start_time), DATE_FORMAT.DAY_MONTH_DAY);
  const accessibleDate = format(parseISO(start_time), "EEEE, MMMM d, yyyy");

  return (
    <Card className="w-full mb-3 dark:border-white relative">
      {topRightContent && (
        <View className="absolute top-2 right-2 z-10">{topRightContent}</View>
      )}
      <CardHeader className="pb-1 pt-4">
        <CardDescription
          className="pb-0"
          accessibilityLabel={`${accessibleDate} ${obtainDateFrame(
            start_time,
            end_time
          )}`}
        >
          {`${formattedDate} ${obtainDateFrame(start_time, end_time)}`}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-3">
        <Text className="font-bold">{equipment_name}</Text>
        <Text>{room_name}</Text>
      </CardContent>
      {renderUser ? (
        renderUser({
          id,
          name: `${first_name} ${last_name}`,
          isCheckIn: Boolean(checkin),
          image: image ?? "",
          membership_id,
          equipment_id,
          user_id,
          onRefresh,
          extraActions,
        })
      ) : (
        <ApptUser
          date={start_time?.split(" ")[0] ?? ""}
          name={`${first_name} ${last_name}`}
          isCheckIn={Boolean(checkin)}
          id={id}
          extraActions={extraActions}
          image={image ?? ""}
          membership_id={membership_id}
          equipment_id={equipment_id}
          user_id={user_id}
          onRefresh={onRefresh}
          isDecremented={Boolean(decrements_sync_id)}
        />
      )}
    </Card>
  );
}
