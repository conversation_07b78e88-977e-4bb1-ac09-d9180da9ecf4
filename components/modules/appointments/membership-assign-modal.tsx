import React, { useState } from "react";
import { View } from "react-native";
import {
  <PERSON>alog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { useUserMembership } from "~/modules/classes/queries/useUserMembership";
import { ComboBox } from "../common/Combo-box";
import {
  AutocompleteDropdownContextProvider,
  AutocompleteDropdownItem,
} from "react-native-autocomplete-dropdown";
import { useUpdateReservation } from "~/modules/classes/mutations/useUpdateReservation";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import { showSuccessToast } from "~/components/toast";

interface MembershipAssignModalProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  reservationId: number;
  userId: number;
  equipmentId: number | string;
  onSuccess?: () => void;
  date?: string;
}

export function MembershipAssignModal({
  isOpen,
  setIsOpen,
  reservationId,
  userId,
  equipmentId,
  onSuccess,
  date,
}: MembershipAssignModalProps) {
  const { trackEvent, EVENTS } = useAnalytics();
  const [selectedMembership, setSelectedMembership] =
    useState<AutocompleteDropdownItem | null>(null);

  const { data: membershipData = [], isLoading } = useUserMembership({
    itemId: String(equipmentId),
    userId: String(userId),
    type: "pt",
    date: date as string,
  });

  const { mutate: updateReservation, isPending } = useUpdateReservation(() => {
    setIsOpen(false);
    onSuccess?.();
    showSuccessToast("Membership assigned successfully");
  });

  const membershipOptions = React.useMemo(
    () =>
      membershipData.map((rec) => ({
        id: String(rec.id),
        title: rec.display_text,
      })),
    [membershipData]
  );

  const handleAssignMembership = () => {
    if (!selectedMembership) return;

    updateReservation({
      reservation_id: reservationId,
      membership_id: selectedMembership.id,
    });

    // Track membership assignment event
    trackEvent(EVENTS.ASSIGN_MEMBERSHIP, {
      reservation_id: reservationId,
      user_id: userId,
      equipment_id: equipmentId,
      membership_id: selectedMembership.id,
      membership_name: selectedMembership.title,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="w-[85vw] mx-auto my-auto h-[32vh] max-h-[280px] min-h-[220px] rounded-xl shadow-lg p-3">
        <DialogHeader className="pb-0">
          <DialogTitle className="text-center">Assign Membership</DialogTitle>
          <DialogDescription className="text-center text-sm">
            Select a membership to assign to this appointment
          </DialogDescription>
        </DialogHeader>

        <AutocompleteDropdownContextProvider>
          <Text className="text-sm font-medium mb-0">Membership</Text>
          <View className="flex h-14">
            <ComboBox
              isLoading={isLoading}
              direction="up"
              data={membershipOptions}
              placeholder="Select membership"
              onSelect={(item) => {
                if (item) {
                  setSelectedMembership(item);
                }
              }}
              value={selectedMembership?.title ?? ""}
              emptyText="No available membership to select"
              accessibilityLabel="Membership selection dropdown"
              accessibilityHint="Select a membership package to assign to this member"
            />
          </View>
        </AutocompleteDropdownContextProvider>

        <DialogFooter className="flex flex-row mt-0 gap-2">
          <Button
            onPress={handleAssignMembership}
            disabled={!selectedMembership || isPending}
            isLoading={isPending}
            label="Assign"
            className="flex-1 py-1"
          />
          <DialogClose asChild>
            <Button
              label="Cancel"
              variant="secondary"
              className="flex-1 py-1"
            />
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
