import { useState } from "react";
import { TouchableOpacity } from "react-native";
import AntDesign from "@expo/vector-icons/AntDesign";
import { CancelModal } from "../classes/cancel-modal";
import { AppointmentCancelModal } from "./appointment-cancel-modal";

interface CancelControlProps {
  id: number;
  isChecked: boolean;
  type?: "class" | "appointment";
}

export function CancelControl({
  id,
  isChecked,
  type = "class",
}: CancelControlProps) {
  const [openCancelModal, setOpenCancelModal] = useState(false);

  if (isChecked) {
    return null;
  }

  return (
    <>
      <TouchableOpacity
        onPress={() => setOpenCancelModal(true)}
        className="px-4 bg-[#E4E7EC] h-16 flex justify-center"
      >
        <AntDesign
          name="close"
          size={20}
          color="black"
          onPress={() => setOpenCancelModal(true)}
        />
      </TouchableOpacity>

      {openCancelModal && type === "class" && (
        <CancelModal
          isOpen={openCancelModal}
          setIsOpen={setOpenCancelModal}
          classId={id}
        />
      )}

      {openCancelModal && type === "appointment" && (
        <AppointmentCancelModal
          isOpen={openCancelModal}
          setIsOpen={setOpenCancelModal}
          appointmentId={id}
        />
      )}
    </>
  );
}
