import { View } from "react-native";
import { Card, CardContent } from "~/components/ui/card";
import { Text } from "~/components/ui/text";
import { format, isValid, parseISO, toDate } from "date-fns";

import { ContactClass } from "~/modules/contact-classes/types";
import { formatTime } from "~/modules/classes/utils";
import { DATE_FORMAT } from "~/constants/date-formats";

export function ContactClassCard({
  class_name,
  class_start_time,
  gym_name,
  reason,
  date,
  send_at,
  sent,
}: ContactClass) {
  const formattedDate = format(parseISO(date), "EEE MMM d, yyyy");

  // const sendDate = send_at
  //   ? format(
  //       parseISO(toDate(send_at).toISOString()).toISOString(),
  //       DATE_FORMAT.DAY_MONTH_DAY
  //     )
  //   : "";
  // const sendTime = send_at ? formatTime(toDate(send_at).toISOString()) : "";

  return (
    <Card className="mb-3">
      <CardContent className="pt-4">
        <View className="flex flex-row gap-2 w-[95%]">
          <Text>Class:</Text>
          <Text className="font-bold w-[95%] text-gray-700 dark:text-gray-300">
            {class_name} - {gym_name}
          </Text>
        </View>
        <View className="space-y-2">
          <View className="flex flex-row items-center gap-2">
            <Text>Date:</Text>
            <Text className="font-bold text-gray-600 dark:text-gray-400">{`${formattedDate}, ${formatTime(
              class_start_time
            )}`}</Text>
          </View>

          <View className="flex flex-row whitespace-pre-wrap w-[90%]">
            <Text className="font-bold text-gray-700 dark:text-gray-300 mb-1 whitespace-nowrap">
              Reason:
            </Text>
            <Text className="text-gray-600 dark:text-gray-400 pl-2 whitespace-break-spaces">
              {reason}
            </Text>
          </View>

          <View className="flex flex-row items-center gap-2">
            <Text>Status:</Text>
            <Text
              className={`font-bold text-gray-600 dark:text-gray-400 ${
                sent ? "text-green-500" : "text-orange-500"
              }`}
            >
              {sent ? "Sent" : "Pending"}
            </Text>
          </View>
          {/* {sendDate && sendTime && (
            <Text className="text-sm text-gray-500 dark:text-gray-500 mt-1">
              Send on: {sendDate} at {sendTime}
            </Text>
          )} */}
        </View>
      </CardContent>
    </Card>
  );
}
