import React, { useState } from "react";
import { <PERSON>, StyleSheet, <PERSON><PERSON>, StatusBar } from "react-native";
import { useColorScheme } from "~/lib/useColorScheme";
import { CardDeck } from "~/components/modules/swipecards/card-deck";
import {
  CustomCard,
  CardItem,
} from "~/components/modules/swipecards/custom-card";

// Sample data for cards
const cardData: CardItem[] = [
  {
    id: 1,
    name: "Morning Yoga Flow",
    start_time: "2023-05-15 07:30:00",
    end_time: "2023-05-15 08:30:00",
    room_name: "Zen Studio",
    instructor: "<PERSON>",
    color: "#1A237E",
    type: "yoga",
  },
  {
    id: 2,
    name: "Core Pilates",
    start_time: "2023-05-16 12:00:00",
    end_time: "2023-05-16 13:00:00",
    room_name: "Fitness Studio",
    instructor: "<PERSON>",
    color: "#069CC3",
    type: "group",
  },
  {
    id: 3,
    name: "Power Cycling",
    start_time: "2023-05-17 17:30:00",
    end_time: "2023-05-17 18:30:00",
    room_name: "Cycle Studio",
    instructor: "<PERSON>",
    color: "#FF9800",
    type: "cycling",
  },
  {
    id: 4,
    name: "HIIT Bootcamp",
    start_time: "2023-05-18 06:00:00",
    end_time: "2023-05-18 07:00:00",
    room_name: "Training Zone",
    instructor: "Sophia Williams",
    color: "#4CAF50",
    type: "cardio",
  },
  {
    id: 5,
    name: "Aqua Cardio",
    start_time: "2023-05-19 15:30:00",
    end_time: "2023-05-19 16:30:00",
    room_name: "Olympic Pool",
    instructor: "Daniel Garcia",
    color: "#E91E63",
    type: "swimming",
  },
];

const SwipeableCardView = () => {
  const { isDarkColorScheme } = useColorScheme();
  // Track current card index for any future functionality
  const [, setCurrentCardIndex] = useState(0);

  const handleCardPress = (item: CardItem) => {
    Alert.alert(
      item.name,
      `You selected ${item.name} with ${item.instructor} in ${item.room_name}`,
      [{ text: "OK" }]
    );
  };

  const renderCard = (item: CardItem) => {
    return <CustomCard item={item} onPress={() => handleCardPress(item)} />;
  };

  return (
    <View
      style={[
        styles.container,
        isDarkColorScheme && { backgroundColor: "#0F172A" },
      ]}
    >
      <StatusBar
        barStyle={isDarkColorScheme ? "light-content" : "dark-content"}
        translucent={true}
        backgroundColor="transparent"
        hidden={true} // Hide the status bar completely
      />
      <CardDeck
        data={cardData}
        renderCard={renderCard}
        onCardChange={setCurrentCardIndex}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F8FAFC",
    // Make sure it covers the entire screen including the header
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 999, // Very high z-index to ensure it's above everything
  },
});

export default function SwipeCardsScreen() {
  return <SwipeableCardView />;
}
