import React from "react";
import { View, TouchableOpacity } from "react-native";
import { Dialog, DialogClose, DialogContent } from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { addMultipleAppointmentsToCalendar } from "~/modules/calendar/calendar-utils";
import { showErrorToast } from "~/components/toast";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import { Ionicons } from "@expo/vector-icons";
import { saveMultipleCalendarEventIds } from "~/modules/calendar/calendar-storage";
import { CalendarAppointment } from "~/modules/appointments/types/calendar";

interface AddMultipleToCalendarModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  appointments: CalendarAppointment[];
}

export function AddMultipleToCalendarModal({
  isOpen,
  setIsOpen,
  appointments,
}: AddMultipleToCalendarModalProps) {
  const { trackEvent, EVENTS } = useAnalytics();
  const [isAdding, setIsAdding] = React.useState(false);

  const handleAddToCalendar = async () => {
    try {
      setIsAdding(true);

      console.log(`Adding ${appointments.length} appointments to calendar`);

      const eventMappings = await addMultipleAppointmentsToCalendar(
        appointments
      );
      const eventCount = Object.keys(eventMappings).length;

      if (eventCount > 0) {
        // Save all calendar event IDs
        await saveMultipleCalendarEventIds(eventMappings);

        // Track the event
        trackEvent(EVENTS.ADD_TO_CALENDAR, {
          appointment_count: appointments.length,
          success_count: eventCount,
          type: "recurring_appointments",
        });

        // Close the modal after a short delay
        setTimeout(() => {
          setIsOpen(false);
        }, 1000);
      } else {
        // If no events were added, keep the modal open
        setIsAdding(false);
        showErrorToast("Failed to add appointments to calendar");
      }
    } catch (err: unknown) {
      const error = err as Error;
      console.error("Error adding to calendar:", error);
      showErrorToast(
        "Failed to add to calendar: " + (error.message || "Unknown error")
      );
      setIsAdding(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="w-[85vw] max-w-[320px] rounded-2xl p-0 overflow-hidden bg-white/95 border-0 shadow-lg">
        <View className="p-5">
          <View className="items-center justify-center mb-4">
            <View className="bg-indigo-100 p-3 rounded-full mb-3">
              <Ionicons name="calendar-outline" size={28} color="#4338ca" />
            </View>
            <Text className="text-gray-800 font-bold text-lg">Share</Text>
            <Text className="text-gray-600 text-center text-sm mt-1">
              Share all {appointments.length} appointments
            </Text>
          </View>

          <View className="mt-4 gap-3">
            <View className="flex-row flex-wrap gap-4 justify-center">
              {/* Calendar sharing option */}
              <TouchableOpacity
                onPress={handleAddToCalendar}
                disabled={isAdding}
                accessibilityRole="button"
                accessibilityLabel="Add to calendar"
                accessibilityHint="Adds these appointments to your device calendar"
                className="items-center"
              >
                <View className="w-[60px] h-[60px] bg-indigo-600 rounded-lg items-center justify-center mb-1">
                  {isAdding ? (
                    <View className="items-center justify-center">
                      <Text className="text-white text-xs">Adding...</Text>
                    </View>
                  ) : (
                    <Ionicons name="calendar-outline" size={28} color="white" />
                  )}
                </View>
                <Text className="text-xs text-center">Calendar</Text>
              </TouchableOpacity>

              {/* Space for additional sharing buttons in the future */}
            </View>

            <DialogClose asChild>
              <Button
                className="w-full bg-gray-100 rounded-lg h-10 justify-center mt-2"
                textClassName="text-gray-700 text-sm"
                label="Cancel"
                variant="secondary"
                onPress={() => {
                  // Track the skip event
                  trackEvent(EVENTS.SKIP_ADD_TO_CALENDAR, {
                    appointment_count: appointments.length,
                    type: "recurring_appointments",
                  });
                }}
              />
            </DialogClose>
          </View>
        </View>
      </DialogContent>
    </Dialog>
  );
}
