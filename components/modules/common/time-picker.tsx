import React from "react";
import { View } from "react-native";
import { AutocompleteDropdownItem } from "react-native-autocomplete-dropdown";
import { ComboBox } from "~/components/modules/common/Combo-box";
import { FormLabel, FormMessage } from "~/components/ui/form";

type Hour24 = number;
type Minute = number;
type TimeSlot = {
  hour: Hour24;
  minute: Minute;
};

const MINUTES_IN_HOUR = 60;
const TIME_SLOT_INTERVAL = 30;
const HOURS_IN_DAY = 24;

const formatTo24Hour = ({ hour, minute }: TimeSlot): string =>
  `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`;

const formatTo12Hour = ({ hour, minute }: TimeSlot): string => {
  const period = hour < 12 ? "AM" : "PM";
  const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
  return `${displayHour}:${minute.toString().padStart(2, "0")}${period}`;
};

const createTimeSlot = (hour: Hour24, minute: Minute): TimeSlot => ({
  hour,
  minute,
});

const generateTimeSlotData = (
  intervalMinutes: number = TIME_SLOT_INTERVAL
): TimeSlot[] => {
  const hours = Array.from({ length: HOURS_IN_DAY }, (_, i) => i);
  const minuteIntervals = Array.from(
    { length: MINUTES_IN_HOUR / intervalMinutes },
    (_, i) => i * intervalMinutes
  );

  return hours.flatMap((hour) =>
    minuteIntervals.map((minute) => createTimeSlot(hour, minute))
  );
};

const timeSlotToDropdownItem = (slot: TimeSlot): AutocompleteDropdownItem => ({
  id: formatTo24Hour(slot),
  title: formatTo12Hour(slot),
});

// Main function
export const generateTimeSlots = (
  intervalMinutes?: number
): AutocompleteDropdownItem[] =>
  generateTimeSlotData(intervalMinutes).map(timeSlotToDropdownItem);

interface TimePickerProps {
  value?: { id: string; title: string } | null;
  onChange: (time: { id: string; title: string } | null) => void;
  label?: string;
  placeholder?: string;
  isLoading?: boolean;
  direction?: "down" | "up";
  emptyText?: string;
}

export const TimePicker = ({
  value,
  onChange,
  label = "Time",
  placeholder = "Select time",
  isLoading = false,
  direction = "down",
  emptyText = "No time slots available",
}: TimePickerProps) => {
  const timeSlots = generateTimeSlots();

  const handleSelect = (item: AutocompleteDropdownItem | null) => {
    if (item) {
      onChange({
        id: item.id,
        title: item.title || "",
      });
    } else {
      onChange(null);
    }
  };

  return (
    <View className="space-y-2 w-[60%]">
      <FormLabel>{label}</FormLabel>
      <View className="flex h-14">
        <ComboBox
          isLoading={isLoading}
          direction={direction}
          data={timeSlots}
          placeholder={placeholder}
          onSelect={handleSelect}
          initialValue={value?.id ?? ""}
          value={value?.title ?? ""}
          emptyText={emptyText}
        />
      </View>
      <FormMessage />
    </View>
  );
};
