import React from "react";
import { View } from "react-native";
import { TouchableOpacity } from "react-native-gesture-handler";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import { FormInput, FormLabel, FormMessage } from "~/components/ui/form";
import { format, addMonths } from "date-fns";

import Fontisto from "@expo/vector-icons/Fontisto";
import { useColorScheme } from "~/lib/useColorScheme";
import { noop } from "lodash/fp";

export const RecurringEndDatePicker = ({
  label,
  value,
  onChange,
  startDate,
}: {
  label: string;
  value: Date;
  onChange: (date: Date) => void;
  startDate: Date;
}) => {
  const { isDarkColorScheme } = useColorScheme();

  const [isDatePickerVisible, setDatePickerVisibility] = React.useState(false);

  // Calculate maximum date (3 months from start date)
  const maximumDate = React.useMemo(() => {
    return addMonths(startDate, 3);
  }, [startDate]);

  // Ensure the value is not beyond the maximum date
  React.useEffect(() => {
    if (value > maximumDate) {
      onChange(maximumDate);
    }
  }, [value, maximumDate, onChange]);

  const showDatePicker = () => {
    setDatePickerVisibility(true);
  };

  const hideDatePicker = () => {
    setDatePickerVisibility(false);
  };

  const handleConfirm = (date: Date) => {
    // Ensure the selected date is not beyond the maximum
    const selectedDate = date > maximumDate ? maximumDate : date;
    onChange(selectedDate);
    hideDatePicker();
  };

  return (
    <React.Fragment>
      <FormLabel>{label}</FormLabel>
      <View className="mb-2">
        <FormInput
          name=""
          textContentType="oneTimeCode"
          value={format(value, "EEE MMM d, yyyy")}
          onBlur={noop}
          onChange={noop}
          className="p-4"
          onPress={showDatePicker}
          style={{ height: 50 }}
        />

        <TouchableOpacity
          onPress={showDatePicker}
          style={{
            position: "absolute",
            right: 10,
            top: "50%",
            transform: [{ translateY: -40 }],
          }}
        >
          <Fontisto
            name="date"
            size={20}
            color={isDarkColorScheme ? "white" : "dark"}
            onPress={showDatePicker}
          />
        </TouchableOpacity>
        <DateTimePickerModal
          date={value}
          locale="en-US"
          isDarkModeEnabled={isDarkColorScheme}
          minimumDate={startDate}
          maximumDate={maximumDate}
          display="inline"
          isVisible={isDatePickerVisible}
          mode="date"
          onConfirm={handleConfirm}
          onCancel={hideDatePicker}
          confirmTextIOS="Confirm"
          cancelTextIOS=""
          customCancelButtonIOS={() => <></>}
          textColor={isDarkColorScheme ? "white" : "black"} // Force text color
        />
        <FormMessage />
      </View>
    </React.Fragment>
  );
};
