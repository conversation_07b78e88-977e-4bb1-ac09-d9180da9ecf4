import {
  AutocompleteDropdown,
  AutocompleteDropdownItem,
} from "react-native-autocomplete-dropdown";

import { Text } from "~/components/ui/text";

export const ComboBox = ({
  data,
  initialValue,
  value,
  onSelect,
  placeholder,
  isLoading,
  direction,
  emptyText = "No data available, type to search",
}: {
  data: AutocompleteDropdownItem[];
  initialValue?: string;
  value?: string;
  onSelect?: (item: AutocompleteDropdownItem | null) => void;
  placeholder?: string;
  isLoading?: boolean;
  direction?: "down" | "up";
  emptyText?: string;
}) => {
  // No need for color scheme since we're using white background for both modes

  return (
    <AutocompleteDropdown
      editable={false}
      loading={isLoading ?? undefined}
      initialValue={{
        id: String(initialValue) ?? "",
      }}
      dataSet={data}
      textInputProps={{
        placeholder: placeholder ?? "",
        placeholderTextColor: "black",
        value: value ?? undefined,
        allowFontScaling: false, // Disable font scaling for combo box input
        style: {
          color: "black",
          padding: 0,
          margin: 0,
          height: 36,
          fontSize: 16, // Force specific font size
        },
      }}
      direction={direction}
      onSelectItem={onSelect}
      inputContainerStyle={{
        padding: 0,
        width: "100%",
        backgroundColor: "white",
      }}
      suggestionsListContainerStyle={{
        backgroundColor: "white",
      }}
      suggestionsListTextStyle={{
        color: "black",
        fontSize: 16, // Force specific font size for suggestions
      }}
      containerStyle={{
        flex: 1,
        width: "100%",
        borderColor: "gray",
        borderWidth: 1,
        borderRadius: 5,
        padding: 0,
        margin: 0,
        height: 38,
      }}
      EmptyResultComponent={
        <Text className="text-center text-black">{emptyText}</Text>
      }
    />
  );
};
