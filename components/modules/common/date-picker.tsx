import React from "react";
import { View } from "react-native";
import { TouchableOpacity } from "react-native-gesture-handler";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import { FormInput, FormLabel, FormMessage } from "~/components/ui/form";
import { format } from "date-fns";

import Fontisto from "@expo/vector-icons/Fontisto";
import { useColorScheme } from "~/lib/useColorScheme";
import { noop } from "lodash/fp";

export const DatePicker = ({
  label,
  value,
  onChange,
  minDate = new Date(),
  mode = "date",
}: {
  label: string;
  value: Date;
  onChange: (date: Date) => void;
  minDate?: Date;
  mode?: "date" | "time" | "datetime";
}) => {
  const { isDarkColorScheme } = useColorScheme();

  const [isDatePickerVisible, setDatePickerVisibility] = React.useState(false);

  const showDatePicker = () => {
    setDatePickerVisibility(true);
  };

  const hideDatePicker = () => {
    setDatePickerVisibility(false);
  };

  const handleConfirm = (date: Date) => {
    onChange(date);
    hideDatePicker();
  };

  const formattedValue =
    mode === "date"
      ? format(value, "EEE MMM d, yyyy")
      : format(value, "EEE MMM d, yyyy, h:mm a");

  // Clean label for accessibility (remove asterisk)
  const cleanLabel = label.replace(/\*$/, "").trim();
  const isRequired = label.includes("*");

  const accessibilityLabel = `${cleanLabel}${
    isRequired ? ", required" : ""
  }, selected date is ${format(value, "EEEE, MMMM d, yyyy")}${
    mode !== "date" ? ` at ${format(value, "h:mm a")}` : ""
  }. Double tap to change.`;

  return (
    <React.Fragment>
      <FormLabel
        accessibilityLabel={cleanLabel + (isRequired ? ", required field" : "")}
      >
        {label}
      </FormLabel>
      <View className="mb-2">
        <FormInput
          name=""
          textContentType="oneTimeCode"
          value={formattedValue}
          onBlur={noop}
          onChange={noop}
          className="p-4"
          onPress={showDatePicker}
          style={{ height: 50 }}
          accessibilityLabel={accessibilityLabel}
          accessibilityRole="button"
          accessibilityHint="Opens date picker to select a new date"
        />

        <TouchableOpacity
          onPress={showDatePicker}
          style={{
            position: "absolute",
            right: 10,
            top: "50%",
            transform: [{ translateY: -40 }],
          }}
          accessibilityLabel="Open date picker"
          accessibilityRole="button"
          accessibilityHint="Opens date picker to select a new date"
        >
          <Fontisto
            name="date"
            size={20}
            color={isDarkColorScheme ? "white" : "dark"}
            onPress={showDatePicker}
          />
        </TouchableOpacity>
        <DateTimePickerModal
          date={value}
          locale="en_GB"
          isDarkModeEnabled={isDarkColorScheme}
          minimumDate={minDate}
          display="inline"
          isVisible={isDatePickerVisible}
          mode={mode}
          onConfirm={handleConfirm}
          onCancel={hideDatePicker}
          confirmTextIOS="Confirm"
          cancelTextIOS="Cancel"
          customCancelButtonIOS={() => <></>}
          textColor={isDarkColorScheme ? "white" : "black"} // Force text color
          // Add accessibility props for the modal
          accessibilityLabel={`Date picker for ${label}`}
          accessibilityViewIsModal={true}
        />
        <FormMessage />
      </View>
    </React.Fragment>
  );
};
