import { useSession } from "~/modules/login/auth-provider";
import { View } from "react-native";
import { Text } from "~/components/ui/text";

export const WelcomeSection = ({
  count,
  salutation,
  message,
  isCapitalized,
}: {
  count?: number;
  salutation?: string;
  message?: string;
  isCapitalized?: boolean;
}) => {
  const { data } = useSession();

  return (
    <View className="mb-4">
      <Text className="text-2xl font-extrabold text-[#1A237E] mb-1 dark:text-white">
        {salutation}{" "}
        {isCapitalized ? data?.first_name?.toUpperCase() : data?.first_name}
      </Text>
      {message && (
        <Text className="text-base font-extrabold text-[#1A237E] dark:text-white">
          YOU HAVE{" "}
          <Text className="text-[#FF9800] font-bold dark:text-white">
            {count}
          </Text>{" "}
          {message}
        </Text>
      )}
    </View>
  );
};
