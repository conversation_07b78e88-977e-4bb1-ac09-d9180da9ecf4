import React from "react";
import { Text } from "~/components/ui/text";
import { Card, CardContent } from "~/components/ui/card";

interface StatsCardProps {
  count: number | string;
  title: string;
  className?: string;
  subtitle?: string;
}

export function StatsCard({
  count,
  title,
  className,
  subtitle,
}: StatsCardProps) {
  // Determine if count is a string that should be treated as a title
  const isCountAsTitle = typeof count === "string" && count.length > 10;

  return (
    <Card className={`border-blue-400 dark:border-white ${className}`}>
      <CardContent className="p-3 flex items-center justify-center">
        {isCountAsTitle ? (
          <>
            <Text
              className="text-xs font-bold text-[#1A237E] dark:text-white text-center uppercase mb-1"
              numberOfLines={2}
            >
              {count}
            </Text>
            <Text
              className="text-xs text-[#1A237E] dark:text-white text-center"
              numberOfLines={2}
            >
              {title}
            </Text>
          </>
        ) : (
          <>
            <Text className="text-2xl font-bold text-[#1A237E] dark:text-white">
              {count}
            </Text>
            <Text
              className="text-xs font-bold text-[#1A237E] dark:text-white text-center uppercase"
              numberOfLines={2}
            >
              {title}
            </Text>
          </>
        )}
        {subtitle && (
          <Text
            className="text-xs text-gray-600 dark:text-gray-300 text-center mt-1"
            numberOfLines={2}
          >
            {subtitle}
          </Text>
        )}
      </CardContent>
    </Card>
  );
}
