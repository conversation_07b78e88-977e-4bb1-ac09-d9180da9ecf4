import React, { PropsWithChildren } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  useWindowDimensions,
  PixelRatio,
} from "react-native";
import {
  useFontScale,
  getControlledFontSizeStatic,
} from "~/lib/font-scale-context";

const { width } = Dimensions.get("window");
const CARD_WIDTH = Math.min(600, width - 32);

export const LoginCard = ({ children }: PropsWithChildren) => {
  const { width: windowWidth, height: windowHeight } = useWindowDimensions();
  const isLandscape = windowWidth > windowHeight;

  // Get font scale context for responsive sizing
  let fontScaleContext;
  try {
    fontScaleContext = useFontScale();
  } catch {
    fontScaleContext = null;
  }

  // Adjust card sizing based on font scale to prevent layout breaks
  const fontScale = PixelRatio.getFontScale();
  const isLargeFontScale = fontScale > 1.3;

  // Reduce card width and padding when font scale is large
  const cardWidthMultiplier = isLargeFontScale ? 0.95 : 0.9;
  const cardWidth = Math.min(600, windowWidth * cardWidthMultiplier);

  // Reduce padding when font scale is large to preserve layout
  const basePadding = windowWidth < 380 ? 20 : 40;
  const cardPadding = isLargeFontScale
    ? Math.max(16, basePadding * 0.8)
    : basePadding;

  // Refactor to remove Stylesheet.create from here
  const dynamicStyles = StyleSheet.create({
    card: {
      width: cardWidth,
      padding: cardPadding,
      maxHeight: isLandscape ? windowHeight * 0.9 : undefined,
    },
  });

  return (
    <View style={styles.container}>
      <View style={[styles.card, dynamicStyles.card]}>
        <View style={styles.content}>{children}</View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingLeft: 10,
    paddingRight: 10,
  },
  card: {
    width: CARD_WIDTH,
    padding: 40,
    backgroundColor: "white",
    borderRadius: 48,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 5,
    position: "relative",
  },

  content: {
    gap: 5,
    zIndex: 2,
  },
});

export default LoginCard;
