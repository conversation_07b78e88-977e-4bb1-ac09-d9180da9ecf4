import React from "react";
import { View } from "react-native";
import { format, parseISO } from "date-fns";
import { obtainDateFrame } from "~/modules/classes/utils";
import { Text } from "~/components/ui/text";

import MaterialIcons from "@expo/vector-icons/MaterialIcons";

interface ClassHeaderProps {
  name: string;
  startTime: string;
  endTime: string;
  roomName: string;
  date?: string;
  cancelled?: boolean;
  is_class_subbed?: boolean;
}

export function ClassHeader({
  name,
  startTime,
  endTime,
  roomName,
  cancelled,
  is_class_subbed,
  date,
}: ClassHeaderProps) {
  return (
    <View>
      <View className="flex flex-row justify-between">
        <Text
          className={`text-[#1A237E] text-2xl font-bold dark:text-white ${
            cancelled ? "line-through" : ""
          } }`}
        >
          {name?.toUpperCase()}
        </Text>
        {is_class_subbed && (
          <View className="flex flex-row items-center">
            <MaterialIcons name="swap-calls" size={14} color="#069CC3" />
            <Text className="pl-1 text-xs text-gray-500 dark:text-gray-400">
              Sub Found
            </Text>
          </View>
        )}
      </View>

      <Text className={`mt-2 ${cancelled ? "line-through" : ""}`}>
        {date
          ? format(parseISO(date), "E MMM, d")
          : format(new Date(), "E MMM, d")}
      </Text>
      <Text className={`${cancelled ? "line-through" : ""}`}>
        {obtainDateFrame(startTime, endTime)}
      </Text>
      <Text className={`${cancelled ? "line-through" : ""}`}>{roomName}</Text>
    </View>
  );
}
