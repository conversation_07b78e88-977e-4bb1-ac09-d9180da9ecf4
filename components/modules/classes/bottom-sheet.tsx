import { BlurView } from "expo-blur";
import { useNavigation } from "expo-router";
import { PropsWithChildren } from "react";
import { DimensionValue, Pressable, SafeAreaView, View } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";

import AntDesign from "@expo/vector-icons/AntDesign";
import { Text } from "~/components/ui/text";
import { useColorScheme } from "~/lib/useColorScheme";

export const BottomSheet = ({
  children,
  title,
  height,
}: PropsWithChildren<{ title?: string; height?: DimensionValue }>) => {
  const navigation = useNavigation();

  const { isDarkColorScheme } = useColorScheme();
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaView className="flex-1">
        <Pressable className="flex-1" onPress={() => navigation.goBack()}>
          <View />
        </Pressable>
        <BlurView
          experimentalBlurMethod="dimezisBlurView"
          intensity={isDarkColorScheme ? 60 : 95}
          tint="light"
          style={{
            height: height ?? "auto",
            width: "100%",
            position: "absolute",
            bottom: 0,
            elevation: 8,
            shadowColor: "#000",
            shadowRadius: 8,
            shadowOpacity: 0.15,
            padding: 16,
          }}
        >
          <View className="flex-row justify-between">
            <View className="flex-1">
              {title && <Text className="text-lg font-bold">{title}</Text>}
            </View>
            <Pressable
              className="justify-end"
              onPress={() => navigation.goBack()}
            >
              <AntDesign
                name="close"
                size={20}
                color="black"
                accessibilityLabel="Close icon"
              />
            </Pressable>
          </View>
          {children}
        </BlurView>
      </SafeAreaView>
    </GestureHandlerRootView>
  );
};
