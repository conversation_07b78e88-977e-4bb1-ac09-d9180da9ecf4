import { <PERSON> } from "expo-router";
import { TouchableOpacity, View } from "react-native";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
} from "~/components/ui/card";
import { Text } from "~/components/ui/text";
import { ClassScheduleItem } from "~/modules/classes/types";
import { obtainDateFrame } from "~/modules/classes/utils";
import { format, parseISO } from "date-fns";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";

export function ClassRangeCard({
  name,
  start_time,
  end_time,
  id,
  date,
  room_name,
  is_class_subbed,
  subbing_instructor,
  cancelled,
}: ClassScheduleItem) {
  // Format the date to get day of week (short) and day of month
  const dayOfWeek = format(parseISO(date), "EEE").toUpperCase();
  const dayOfMonth = format(parseISO(date), "dd");

  return (
    <Link
      push
      href={{
        pathname: "/(classes)/[id]/",
        params: {
          id,
          start_time,
          end_time,
          name,
          room_name,
          date,
          cancelled: cancelled ? "true" : "",
        },
      }}
      asChild
    >
      <TouchableOpacity>
        <Card
          className={`w-full mb-3 dark:border-white overflow-hidden ${
            cancelled
              ? "bg-red-100 dark:bg-red-900"
              : is_class_subbed
              ? "bg-gray-100 dark:bg-gray-800"
              : ""
          }`}
        >
          <View className="flex flex-row">
            <View className="flex-1">
              <CardHeader className="pb-1 pt-4 flex flex-row justify-between">
                <CardDescription className={cancelled ? "line-through" : ""}>
                  {obtainDateFrame(start_time, end_time)}
                </CardDescription>
                {cancelled ? (
                  <View className="flex flex-row items-center">
                    <MaterialIcons name="cancel" size={20} color="#E53935" />
                  </View>
                ) : subbing_instructor ? (
                  <View className="flex flex-row items-center">
                    <MaterialIcons
                      name="swap-calls"
                      size={20}
                      color="#069CC3"
                    />
                  </View>
                ) : null}
              </CardHeader>
              <CardContent className="pb-3">
                {cancelled ? (
                  <>
                    <Text className="text-xs text-red-600 dark:text-red-400 font-bold">
                      (Cancelled)
                      <Text className={cancelled ? "line-through" : ""}>
                        {name}
                      </Text>
                    </Text>
                  </>
                ) : is_class_subbed ? (
                  <>
                    <Text className="text-xs">
                      (Sub Found) <Text className="font-bold">{name}</Text>
                    </Text>
                  </>
                ) : (
                  <Text className="font-bold">{name}</Text>
                )}
              </CardContent>
            </View>
            <View className="w-16 bg-[#069CC3] justify-center items-center">
              <Text className="text-white text-sm">{dayOfWeek}</Text>
              <Text className="text-white text-lg font-bold">{dayOfMonth}</Text>
            </View>
          </View>
        </Card>
      </TouchableOpacity>
    </Link>
  );
}
