import React from "react";
import { ActivityIndicator } from "react-native";
import { SafeAreaView, SafeAreaProvider } from "react-native-safe-area-context";
import { cn } from "~/lib/utils";

export const Loader = ({
  className,
  size = "large",
  color = "#1A237E",
}: {
  className?: string;
  size?: "large" | "small";
  color?: string;
}) => (
  <SafeAreaProvider>
    <SafeAreaView className={cn("flex  justify-center", className)}>
      <ActivityIndicator color={color} size={size} />
    </SafeAreaView>
  </SafeAreaProvider>
);
