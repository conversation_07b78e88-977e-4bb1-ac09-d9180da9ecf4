import <PERSON>tieView from "lottie-react-native";
import { useState, useEffect, ReactNode } from "react";
import { ScrollView, View } from "react-native";
import { Dialog, DialogContent } from "~/components/ui/dialog";
import { Text } from "~/components/ui/text";

import MaterialIcons from "@expo/vector-icons/MaterialIcons";

import Icon from "@expo/vector-icons/MaterialCommunityIcons";
import { Celebration as CelebrationItem } from "~/modules/classes/types";
import { Image } from "expo-image";

import { getLocalData, shouldShowNotification, storeLocalData } from "./utils";
import { formatClassDate } from "~/modules/classes/utils";

const CELEBRATE_SESSION = "date-session";

const CelebrationSection = ({
  title,
  icon,
  items,
}: {
  title: string;
  icon: ReactNode;
  items?: CelebrationItem[];
}) =>
  items?.length ? (
    <View className="items-center mb-7.5 mt-8">
      {icon}
      <Text className="text-xl font-bold my-2.5 text-gray-800 dark:text-white">
        {title}
      </Text>
      {items.map((person, index) => (
        <Text
          key={index}
          className="text-base my-1.25 text-gray-600 dark:text-white"
        >
          {person?.name || `${person?.first_name} ${person?.last_name}`}
        </Text>
      ))}
    </View>
  ) : null;

const CelebrateImage = () => {
  return (
    <View className="flex items-center mb-0 pb-0">
      <View className="h-20 w-20 bg-[#069cc3] rounded-full top-[-48] absolute ">
        <Image
          source={require("../../../../assets/images/celebrate.gif")}
          style={{
            height: 60,
            width: 60,
            position: "absolute",
            left: "50%",
            top: "50%",
            marginLeft: -20,
            marginTop: -40,
          }}
        />
      </View>
    </View>
  );
};

export const BirthDayCelebration = ({
  styles,
}: {
  styles?: Record<string, string | number>;
}) => {
  return (
    <LottieView
      source={require("../../../../assets/images/confetti.json")}
      style={{
        width: "100%",
        height: "100%",
        position: "absolute",
        ...styles,
      }}
      autoPlay
      loop
    />
  );
};

export interface CelebrationModalProps {
  celebrations?: {
    BIRTHDAYS?: CelebrationItem[];
    FIRST_CLASS?: CelebrationItem[];
    NEW_MEMBER?: CelebrationItem[];
    MILESTONES?: CelebrationItem[];
  };
  classId?: number;
}

export function Celebration({
  celebrations = {},
  classId,
}: CelebrationModalProps) {
  const [isOpen, setIsOpen] = useState(false);

  const {
    BIRTHDAYS = [],
    FIRST_CLASS = [],
    NEW_MEMBER = [],
    MILESTONES = [],
  } = celebrations;

  const showCelebration = shouldShowNotification({
    BIRTHDAYS,
    FIRST_CLASS,
    NEW_MEMBER,
    MILESTONES,
  });

  useEffect(() => {
    const checkIfOpenedToday = async () => {
      const lastSessionId = await getLocalData(CELEBRATE_SESSION);
      const today = formatClassDate();

      if (showCelebration && lastSessionId?.[String(classId)] !== today) {
        setIsOpen(true);
        await storeLocalData(CELEBRATE_SESSION, { [String(classId)]: today });
      }
    };

    checkIfOpenedToday();
  }, [showCelebration, classId]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="dark:border-white ">
        <CelebrateImage />
        <ScrollView className="flex-grow-0 p-4 mt-4">
          <Text className="text-[#4AA3D1] text-2xl text-center font-bold">
            REASONS TO CELEBRATE TODAY
          </Text>
          <CelebrationSection
            title="Today's Birthdays"
            icon=<Icon name="cake-variant" size={30} color="#4AA3D1" />
            items={BIRTHDAYS}
          />
          <CelebrationSection
            title="Welcome New Members"
            icon=<Icon name="hand-wave" size={30} color="#4AA3D1" />
            items={NEW_MEMBER}
          />
          <CelebrationSection
            title="1st Class"
            icon=<MaterialIcons name="celebration" size={30} color="#4AA3D1" />
            items={FIRST_CLASS}
          />
          <CelebrationSection
            title="Milestones"
            icon=<MaterialIcons name="celebration" size={30} color="#4AA3D1" />
            items={MILESTONES}
          />
        </ScrollView>
        <BirthDayCelebration />
      </DialogContent>
    </Dialog>
  );
}
