import { View } from "react-native";
import { Text } from "~/components/ui/text";
import Icon from "@expo/vector-icons/MaterialCommunityIcons";
import { ReactNode } from "react";
import { CelebrationModalProps } from "./celebration";

import MaterialIcons from "@expo/vector-icons/MaterialIcons";

const CelebrationChip = ({
  title,
  icon,
}: {
  title: string;
  icon: ReactNode;
}) => {
  return (
    <View className="bg-[#002966] w-24 flex flex-row  rounded-b-xl ml-2">
      {icon}
      <Text className="text-white text-xs font-bold pt-2">{title}</Text>
    </View>
  );
};

interface CelebrationChipProps extends CelebrationModalProps {
  userId: number;
}

export const CelebrationChips = ({
  userId,
  celebrations,
}: CelebrationChipProps) => {
  const foundBirthday = celebrations?.BIRTHDAYS?.find(
    (birthday) => birthday?.id === userId
  );

  const foundNewUser = celebrations?.NEW_MEMBER?.find(
    (member) => member?.id === userId
  );

  const firstClass = celebrations?.FIRST_CLASS?.find(
    (user) => user?.id === userId
  );

  const milestone = celebrations?.MILESTONES?.find(
    (user) => user?.id === userId
  );

  return (
    <View className="flex flex-row">
      {foundBirthday && (
        <CelebrationChip
          title="Birthday"
          icon=<Icon
            name="cake-variant"
            size={15}
            color="white"
            className="p-2"
          />
        />
      )}
      {foundNewUser && (
        <CelebrationChip
          title="New Join"
          icon=<Icon name="hand-wave" size={15} color="white" className="p-2" />
        />
      )}

      {firstClass && (
        <CelebrationChip
          title="1st Class"
          icon=<MaterialIcons
            name="celebration"
            size={15}
            color="white"
            className="p-2"
          />
        />
      )}

      {milestone && (
        <CelebrationChip
          title={`${milestone?.total_reservations_count_in_period}th Class`}
          icon=<MaterialIcons
            name="celebration"
            size={15}
            color="white"
            className="p-2"
          />
        />
      )}
    </View>
  );
};
