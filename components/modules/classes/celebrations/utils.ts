import { Celebration } from "~/modules/classes/types";

import { isEmpty } from "lodash/fp";
import AsyncStorage from "@react-native-async-storage/async-storage";

export const shouldShowNotification = (celebrations: {
  BIRTHDAYS?: Celebration[];
  FIRST_CLASS?: Celebration[];
  NEW_MEMBER?: Celebration[];
  MILESTONES?: Celebration[];
}) => {
  return (
    !isEmpty(celebrations.BIRTHDAYS) ||
    !isEmpty(celebrations.FIRST_CLASS) ||
    !isEmpty(celebrations.NEW_MEMBER) ||
    !isEmpty(celebrations.MILESTONES)
  );
};

export const getLocalData = async (key: string) => {
  try {
    const jsonValue = await AsyncStorage.getItem(key);
    return jsonValue != null ? JSON.parse(jsonValue) : null;
  } catch (e) {
    throw new Error("sorry error occurred, try again");
  }
};

export const storeLocalData = async (
  key: string,
  value: Record<string, string>
) => {
  try {
    const currentValue = await getLocalData(key);

    if (<PERSON><PERSON><PERSON>(currentValue)) {
      const updatedValue = { ...currentValue, ...value };
      const jsonValue = JSON.stringify(updatedValue);
      return await AsyncStorage.setItem(key, jsonValue);
    }
    const jsonValue = JSON.stringify(value);
    await AsyncStorage.setItem(key, jsonValue);
  } catch (e) {
    throw new Error("sorry error occurred, try again");
  }
};

export const removeLocal = async (key: string) => {
  try {
    return await AsyncStorage.removeItem(key);
  } catch (e) {
    throw new Error("sorry error occurred, try again");
  }
};
