import { TouchableOpacity, View } from "react-native";

import { ReactNode } from "react";
import { cn } from "~/lib/utils";
import { Text } from "~/components/ui/text";

export function Chip({
  label,
  icon,
  onPress,
  className,
}: {
  label: string;
  icon: ReactNode;
  onPress?: () => void;
  className?: string;
}) {
  return (
    <TouchableOpacity onPress={onPress}>
      <View className={"w-20"} collapsable={false}>
        <View
          className={cn(
            "flex flex-col items-center justify-center mt-4 h-20 w-20 border-2 rounded-lg pb-0 dark:border-white",
            className
          )}
        >
          {icon}
        </View>
        <Text className="text-center pb-0 mb-0 text-xs">{label}</Text>
      </View>
    </TouchableOpacity>
  );
}
