import { TouchableOpacity, View } from "react-native";
import { Card, CardContent } from "~/components/ui/card";
import { Text } from "~/components/ui/text";
import { getInitials } from "~/modules/classes/utils";
import { BaseAvatar } from "./avatar";
import { Switch } from "~/components/ui/switch";
import { useCheckInMutation } from "~/modules/classes/mutations/useCheckInMutation";
import { Fragment, ReactNode, useState } from "react";

import AntDesign from "@expo/vector-icons/AntDesign";
import { CancelModal } from "./cancel-modal";

export function ReserveCard({
  name,
  isCheckIn,
  id,
  image,
  children,
}: {
  name: string;
  isCheckIn: boolean;
  id: number;
  image?: string;
  children?: ReactNode;
}) {
  const { mutate: handleCheckIn, isPending } = useCheckInMutation();

  const [checkIn, setCheckIn] = useState(isCheckIn);

  const handleCheckInToggle = () => {
    setCheckIn(!checkIn);
    handleCheckIn({
      id,
      isCheckIn: !isCheckIn,
    });
  };

  const isChecked = isCheckIn ?? checkIn;

  const [openCancelModal, setOpenCancelModal] = useState(false);

  return (
    <Fragment>
      <View className="relative mb-4">
        <Card className="w-full mt-4 ">
          <CardContent className="p-0 flex flex-row justify-between">
            <View className="flex flex-row gap-2 p-3">
              <BaseAvatar url={image} name={getInitials(name)} />
              <Text className="font-bold mt-2">{name}</Text>
            </View>
            <View
              className={`flex flex-row items-center gap-4 ${
                isChecked ? "pr-2" : ""
              } `}
            >
              <Switch
                checked={isChecked}
                onCheckedChange={handleCheckInToggle}
                className="pr-4"
                isLoading={isPending}
              />

              {!isChecked && (
                <TouchableOpacity
                  onPress={() => setOpenCancelModal(true)}
                  className="px-4 bg-[#E4E7EC] h-full flex items-center justify-center"
                >
                  <AntDesign
                    name="close"
                    size={20}
                    color="black"
                    onPress={() => setOpenCancelModal(true)}
                  />
                </TouchableOpacity>
              )}
            </View>
          </CardContent>
        </Card>
        {children}
      </View>

      {openCancelModal && (
        <CancelModal
          isOpen={openCancelModal}
          setIsOpen={setOpenCancelModal}
          classId={id}
        />
      )}
    </Fragment>
  );
}
