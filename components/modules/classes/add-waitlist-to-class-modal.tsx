import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { useWaitlistToClass } from "~/modules/classes/queries/useWaitlistToClass";

export function AddWaitlistToClassModal({
  isOpen,
  setIsOpen,
  waitlistId,
}: {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  waitlistId: number;
}) {
  const { mutate: handlePromoteToClass, isPending } = useWaitlistToClass(() => {
    setIsOpen(false);
  });

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle> Add User to the class?</DialogTitle>
          <DialogDescription className="text-yellow-500 mt-2 font-bold">
            This will move the user off the waitlist to the class regardless of
            the spots available. Do you want to proceed?
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex flex-row mt-2">
          <Button
            onPress={() => handlePromoteToClass(waitlistId)}
            disabled={isPending}
            variant={"default"}
            className="bg-yellow-500"
            label={isPending ? "Adding..." : "Add to Class"}
          />

          <DialogClose asChild>
            <Button label="Close" variant={"secondary"} />
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
