import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Text } from "~/components/ui/text";
import { StyleSheet } from "react-native";

export function BaseAvatar({
  url,
  name,
  size,
}: {
  url?: string;
  name: string;
  size?: number;
}) {
  const styles = StyleSheet.create({
    avatar: size
      ? {
          width: size,
          height: size,
        }
      : {},
  });

  return (
    <Avatar alt="upace" style={styles.avatar}>
      <AvatarImage source={{ uri: url }} />
      <AvatarFallback>
        <Text style={{ fontSize: size ? size / 3 : undefined }}>{name}</Text>
      </AvatarFallback>
    </Avatar>
  );
}
