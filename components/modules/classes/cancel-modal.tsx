import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { Text } from "~/components/ui/text";
import { useCancelReservation } from "~/modules/classes/mutations/useCancelReservation";

export function CancelModal({
  isOpen,
  setIsOpen,
  classId,
}: {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  classId: number;
}) {
  const { mutate: handleCancellation, isPending } = useCancelReservation(() => {
    setIsOpen(false);
  });

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Cancel Reservation?</DialogTitle>
          <DialogDescription className="text-red-500 mt-2 font-bold">
            This will cancel the reservation permanently and cannot be undone.
            Proceed?
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex flex-row mt-2">
          <Button
            onPress={() => handleCancellation(classId)}
            disabled={isPending}
            variant={"destructive"}
            label={isPending ? "Cancelling..." : "Cancel Reservation"}
          />

          <DialogClose asChild>
            <Button label="Close" variant={"secondary"} />
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
