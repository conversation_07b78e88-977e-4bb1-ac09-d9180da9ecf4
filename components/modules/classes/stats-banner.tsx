import React from "react";
import { View } from "react-native";
import { Feather } from "@expo/vector-icons";
import { Text } from "~/components/ui/text";
import { useColorScheme } from "~/lib/useColorScheme";

interface StatsBannerProps {
  reservationsCount?: number;
  waitlistCount?: number;
  spotsAvailable?: number;
}

export function StatsBanner({
  reservationsCount,
  waitlistCount,
  spotsAvailable,
}: StatsBannerProps) {
  const { isDarkColorScheme } = useColorScheme();

  return (
    <View className="mt-4 h-10 w-full bg-[#069CC3] dark:bg-white rounded-lg">
      <View className="flex flex-row items-center">
        <Feather
          name="users"
          size={20}
          color={isDarkColorScheme ? "black" : "white"}
          className="p-2"
        />
        <Text className="text-white dark:text-black">
          Reservations:{" "}
          <Text className="font-bold text-white dark:text-black">
            {reservationsCount}
          </Text>{" "}
        </Text>
        <Text className="text-white dark:text-black">
          Waitlist:{" "}
          <Text className="font-bold text-white dark:text-black">
            {waitlistCount}
          </Text>
          {"  "}
        </Text>
        <Text className="text-white dark:text-black">
          Spots Available:{" "}
          <Text className="font-bold text-white dark:text-black">
            {spotsAvailable}
          </Text>
        </Text>
      </View>
    </View>
  );
}
