import { View } from "react-native";
import { useCallback } from "react";

import Feather from "@expo/vector-icons/Feather";
import { Input } from "~/components/ui/input";
import { Pressable } from "react-native";

import SimpleLineIcons from "@expo/vector-icons/SimpleLineIcons";
import { cn } from "~/lib/utils";
import { useColorScheme } from "~/lib/useColorScheme";
import { useAnalytics } from "~/modules/hooks/useAnalytics";

export function SearchInput({
  searchTerm,
  setSearchTerm,
  placeholder = "Search",
  className,
  searchContext = "general",
}: {
  searchTerm: string;
  setSearchTerm: (text: string) => void;
  className?: string;
  placeholder?: string;
  searchContext?: string;
}) {
  const { isDarkColorScheme } = useColorScheme();
  const { trackEvent, EVENTS } = useAnalytics();

  // Track search with debounce
  const handleSearch = useCallback(
    (text: string) => {
      setSearchTerm(text);

      // Only track non-empty searches and when the user has typed at least 3 characters
      if (text && text.length >= 3) {
        trackEvent(EVENTS.SEARCH, {
          search_term: text,
          search_context: searchContext,
        });
      }
    },
    [setSearchTerm, trackEvent, searchContext]
  );

  return (
    <View className={cn("pt-3 pr-3 mb-5 relative", className)}>
      <View className="relative">
        <Feather
          name="search"
          size={20}
          color="red"
          className="absolute left-1 top-2.5 text-red-400"
        />
        <Input
          placeholder={placeholder}
          value={searchTerm}
          onChangeText={handleSearch}
        />
        {searchTerm?.length > 0 && (
          <Pressable
            onPress={() => setSearchTerm("")}
            className="absolute right-3 top-2.5"
          >
            <SimpleLineIcons
              name="close"
              size={20}
              color={isDarkColorScheme ? "white" : "black"}
              className="text-gray-400"
            />
          </Pressable>
        )}
      </View>
    </View>
  );
}
