import React from "react";
import { View } from "react-native";
import Ionicons from "@expo/vector-icons/Ionicons";

import FontAwesome5 from "@expo/vector-icons/FontAwesome5";

import Entypo from "@expo/vector-icons/Entypo";

import FontAwesome from "@expo/vector-icons/FontAwesome";

import { Chip } from "./chip";
import { useColorScheme } from "~/lib/useColorScheme";
import { router } from "expo-router";
import { useAnalytics } from "~/modules/hooks/useAnalytics";

interface ActionChipsProps {
  onOccupancyPress: () => void;
  onAddReservation: () => void;
  onBarcodePress: () => void;
  onClassPress?: () => void;
  classId?: string | number;
  className?: string;
}

export function ActionChips({
  onOccupancyPress,
  onAddReservation,
  onBarcodePress,
  onClassPress,
  classId,
  className,
}: ActionChipsProps) {
  const { isDarkColorScheme } = useColorScheme();
  const { trackEvent, EVENTS } = useAnalytics();

  // Helper function to track class actions
  const trackClassAction = (action: string) => {
    if (classId) {
      trackEvent(EVENTS.CLASS_ACTION, {
        class_id: classId,
        class_name: className,
        action: action,
      });
    }
  };

  return (
    <View className="flex flex-row flex-wrap justify-between">
      <Chip
        onPress={() => {
          trackClassAction("barcode_check_in");
          onBarcodePress();
        }}
        className="border-[#1E2059] "
        label="Barcode Check in"
        icon={
          <FontAwesome
            name="barcode"
            size={20}
            color={isDarkColorScheme ? "white" : "black"}
          />
        }
      />
      <Chip
        className="border-[#069CC3]"
        onPress={() => {
          trackClassAction("add_reservation");
          onAddReservation();
        }}
        label="Add Reservation"
        icon={
          <Ionicons
            name="add-circle-outline"
            size={20}
            color={isDarkColorScheme ? "white" : "#069CC3"}
          />
        }
      />
      <Chip
        className="border-[#E93B92]"
        onPress={() => {
          trackClassAction("input_occupancy");
          onOccupancyPress();
        }}
        label="Input Occupancy"
        icon={
          <FontAwesome5
            name="check-circle"
            size={20}
            color={isDarkColorScheme ? "white" : "#E93B92"}
          />
        }
      />
      <Chip
        onPress={() => {
          trackClassAction("back_to_classes");
          onClassPress?.();
        }}
        className="border-[#F79720]"
        label="View My Classes"
        icon={
          <Entypo
            name="text-document"
            size={20}
            color={isDarkColorScheme ? "white" : "#F79720"}
          />
        }
      />
    </View>
  );
}
