import React from "react";
import { StyleSheet, Dimensions, View } from "react-native";
import { PanGestureHandler } from "react-native-gesture-handler";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedGestureHandler,
  withSpring,
  withTiming,
  runOnJS,
  interpolate,
  Extrapolate,
} from "react-native-reanimated";

const { width, height } = Dimensions.get("window");

type SwipeableCardProps = {
  children: React.ReactNode;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  index: number;
  activeIndex: number;
};

export function SwipeableCard({
  children,
  onSwipeLeft,
  onSwipeRight,
  index,
  activeIndex,
}: SwipeableCardProps) {
  const translateX = useSharedValue(0);
  const cardScale = useSharedValue(1);
  const cardOpacity = useSharedValue(1);

  // Calculate the position based on index and activeIndex
  const position = index - activeIndex;

  // Set initial position
  const isVisible = position >= -1 && position <= 1;

  // Rotation for the card
  const rotation = useSharedValue(0);

  // Reset animation values when activeIndex changes
  React.useEffect(() => {
    translateX.value = 0;
    rotation.value = 0;
    cardScale.value = 1;
    cardOpacity.value = 1;
  }, [activeIndex]);

  const gestureHandler = useAnimatedGestureHandler({
    onStart: (_, ctx: any) => {
      ctx.startX = translateX.value;
    },
    onActive: (event, ctx) => {
      if (index === activeIndex) {
        translateX.value = ctx.startX + event.translationX;

        // Rotate the card slightly based on swipe direction
        rotation.value = interpolate(
          event.translationX,
          [-width / 2, 0, width / 2],
          [-10, 0, 10],
          Extrapolate.CLAMP
        );

        // Scale and opacity based on swipe distance
        const swipeDistance = Math.abs(event.translationX);
        cardScale.value = interpolate(
          swipeDistance,
          [0, width / 2],
          [1, 0.95],
          Extrapolate.CLAMP
        );

        cardOpacity.value = interpolate(
          swipeDistance,
          [0, width / 2],
          [1, 0.8],
          Extrapolate.CLAMP
        );
      }
    },
    onEnd: (event) => {
      if (index === activeIndex) {
        if (event.velocityX < -500 || event.translationX < -width / 3) {
          // Swipe left
          translateX.value = withTiming(-width * 1.5, { duration: 250 });
          rotation.value = withTiming(-20, { duration: 250 });
          if (onSwipeLeft) {
            runOnJS(onSwipeLeft)();
          }
        } else if (event.velocityX > 500 || event.translationX > width / 3) {
          // Swipe right
          translateX.value = withTiming(width * 1.5, { duration: 250 });
          rotation.value = withTiming(20, { duration: 250 });
          if (onSwipeRight) {
            runOnJS(onSwipeRight)();
          }
        } else {
          // Return to center
          translateX.value = withSpring(0);
          rotation.value = withSpring(0);
        }

        // Reset scale and opacity
        cardScale.value = withSpring(1);
        cardOpacity.value = withSpring(1);
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    let translateValue = 0;
    let scaleValue = 1;
    let opacityValue = 1;
    let rotateValue = "0deg";
    let translateYValue = 0;

    // Position cards based on their index relative to activeIndex
    if (position === 0) {
      // Current card
      translateValue = translateX.value;
      scaleValue = cardScale.value;
      opacityValue = cardOpacity.value;
      rotateValue = `${rotation.value}deg`;
    } else if (position === 1) {
      // Next card (behind current)
      translateValue = 0;
      scaleValue = 0.9;
      opacityValue = 0.7;
      translateYValue = 15;
    } else if (position === -1) {
      // Previous card (not visible)
      translateValue = 0;
      scaleValue = 0.85;
      opacityValue = 0;
      translateYValue = 30;
    } else {
      // Cards outside the visible range
      translateValue = 0;
      scaleValue = 0.8;
      opacityValue = 0;
      translateYValue = 30;
    }

    return {
      transform: [
        { translateX: translateValue },
        { translateY: translateYValue },
        { scale: scaleValue },
        { rotate: rotateValue },
      ],
      opacity: opacityValue,
      zIndex: 10 - Math.abs(position),
      position: "absolute",
      width: width,
      height: "100%", // Take full height of container
    };
  });

  if (!isVisible) return null;

  return (
    <PanGestureHandler onGestureEvent={gestureHandler}>
      <Animated.View style={[styles.card, animatedStyle]}>
        {children}
      </Animated.View>
    </PanGestureHandler>
  );
}

const styles = StyleSheet.create({
  card: {
    width: width,
    height: "100%",
    alignSelf: "center",
    justifyContent: "center",
    overflow: "hidden",
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000, // Very high z-index to ensure it's above everything
  },
});
