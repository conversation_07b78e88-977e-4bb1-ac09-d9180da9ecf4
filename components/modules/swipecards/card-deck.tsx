import React, { useState } from "react";
import { View, StyleSheet, TouchableOpacity, Dimensions } from "react-native";
import { Text } from "~/components/ui/text";
import { useColorScheme } from "~/lib/useColorScheme";
import { SwipeableCard } from "./swipeable-card";
import Ionicons from "@expo/vector-icons/Ionicons";

const { width, height } = Dimensions.get("window");

interface CardDeckProps<T> {
  data: T[];
  renderCard: (item: T, index: number) => React.ReactNode;
  onCardChange?: (index: number) => void;
}

export function CardDeck<T>({
  data,
  renderCard,
  onCardChange,
}: CardDeckProps<T>) {
  const [activeIndex, setActiveIndex] = useState(0);
  const { isDarkColorScheme } = useColorScheme();

  const handleSwipeLeft = () => {
    if (activeIndex < data.length - 1) {
      // Move to next card
      setActiveIndex(activeIndex + 1);
      onCardChange?.(activeIndex + 1);
    } else {
      // Wrap around to the first card
      setActiveIndex(0);
      onCardChange?.(0);
    }
  };

  const handleSwipeRight = () => {
    if (activeIndex > 0) {
      // Move to previous card
      setActiveIndex(activeIndex - 1);
      onCardChange?.(activeIndex - 1);
    } else {
      // Wrap around to the last card
      setActiveIndex(data.length - 1);
      onCardChange?.(data.length - 1);
    }
  };

  const renderIndicators = () => {
    return (
      <View style={styles.indicatorContainer}>
        {data.map((_, index) => (
          <View
            key={index}
            style={[
              styles.indicator,
              {
                backgroundColor:
                  index === activeIndex
                    ? isDarkColorScheme
                      ? "white"
                      : "#1A237E"
                    : isDarkColorScheme
                    ? "#555555"
                    : "#E0E0E0",
                width: index === activeIndex ? 20 : 10,
              },
            ]}
          />
        ))}
      </View>
    );
  };

  const renderPageIndicator = () => {
    return (
      <View style={styles.pageIndicatorContainer}>
        <Text style={styles.pageIndicatorText}>
          {activeIndex + 1} / {data.length}
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.cardContainer}>
        {data.map((item, index) => (
          <SwipeableCard
            key={index}
            index={index}
            activeIndex={activeIndex}
            onSwipeLeft={handleSwipeLeft}
            onSwipeRight={handleSwipeRight}
          >
            {renderCard(item, index)}
          </SwipeableCard>
        ))}
      </View>

      <View style={styles.controlsContainer}>
        {renderIndicators()}
        {renderPageIndicator()}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 999, // Very high z-index to ensure it's above everything
  },
  cardContainer: {
    width: width,
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
  },
  controlsContainer: {
    position: "absolute",
    bottom: 20,
    width: "100%",
    paddingHorizontal: 20,
    zIndex: 100,
  },
  indicatorContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginVertical: 10,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
    borderRadius: 20,
    padding: 8,
    paddingHorizontal: 12,
  },
  indicator: {
    height: 10,
    width: 10,
    borderRadius: 5,
    marginHorizontal: 5,
  },
  pageIndicatorContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginTop: 5,
  },
  pageIndicatorText: {
    color: "white",
    fontSize: 14,
    fontWeight: "bold",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 15,
    overflow: "hidden",
  },
});
