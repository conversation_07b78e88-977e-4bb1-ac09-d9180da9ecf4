import React from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ImageBackground,
} from "react-native";
import { Text } from "~/components/ui/text";
import { useColorScheme } from "~/lib/useColorScheme";
import { format } from "date-fns";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import FontAwesome from "@expo/vector-icons/FontAwesome";
import Ionicons from "@expo/vector-icons/Ionicons";
import AntDesign from "@expo/vector-icons/AntDesign";
import { LinearGradient } from "expo-linear-gradient";

const { width, height } = Dimensions.get("window");

export interface CardItem {
  id: number;
  name: string;
  start_time: string;
  end_time: string;
  room_name: string;
  instructor: string;
  color: string;
  type?: string;
}

interface CustomCardProps {
  item: CardItem;
  onPress?: () => void;
}

// Background colors for different class types (instead of images)
const backgroundColors = {
  yoga: "#8E24AA",
  cardio: "#D81B60",
  cycling: "#FB8C00",
  swimming: "#039BE5",
  group: "#43A047",
  default: "#1A237E",
};

export function CustomCard({ item, onPress }: CustomCardProps) {
  const { isDarkColorScheme } = useColorScheme();

  // Determine which icon to show based on the card type
  const getCardIcon = () => {
    switch (item.type) {
      case "yoga":
        return (
          <MaterialIcons name="self-improvement" size={36} color="white" />
        );
      case "cardio":
        return <FontAwesome name="heartbeat" size={36} color="white" />;
      case "cycling":
        return <MaterialIcons name="directions-bike" size={36} color="white" />;
      case "swimming":
        return <MaterialIcons name="pool" size={36} color="white" />;
      case "group":
        return <FontAwesome name="group" size={36} color="white" />;
      default:
        return <MaterialIcons name="fitness-center" size={36} color="white" />;
    }
  };

  // Get background color based on class type
  const getBackgroundColor = () => {
    switch (item.type) {
      case "yoga":
        return backgroundColors.yoga;
      case "cardio":
        return backgroundColors.cardio;
      case "cycling":
        return backgroundColors.cycling;
      case "swimming":
        return backgroundColors.swimming;
      case "group":
        return backgroundColors.group;
      default:
        return backgroundColors.default;
    }
  };

  const formattedDate = format(new Date(item.start_time), "EEE, MMM d");
  const startTime = format(new Date(item.start_time), "h:mm a");
  const endTime = format(new Date(item.end_time), "h:mm a");
  const dayNumber = format(new Date(item.start_time), "dd");
  const monthName = format(new Date(item.start_time), "MMM").toUpperCase();

  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={1}
      style={styles.container}
    >
      <View
        style={[
          styles.backgroundImage,
          { backgroundColor: getBackgroundColor() },
        ]}
      >
        <LinearGradient
          colors={["rgba(0,0,0,0.3)", "rgba(0,0,0,0.6)", "rgba(0,0,0,0.85)"]}
          style={styles.gradient}
        >
          {/* Top section with class info */}
          <View style={styles.topSection}>
            <View style={styles.dateContainer}>
              <Text style={styles.dayNumber}>{dayNumber}</Text>
              <Text style={styles.monthName}>{monthName}</Text>
            </View>
          </View>

          {/* Class name and time - centered */}
          <View style={styles.centerSection}>
            <Text style={styles.className}>{item.name}</Text>
            <Text style={styles.classTime}>
              {formattedDate} • {startTime} - {endTime}
            </Text>

            {/* Swipe indicator */}
            <View style={styles.swipeIndicator}>
              <AntDesign
                name="swapleft"
                size={20}
                color="rgba(255, 255, 255, 0.7)"
              />
              <Text style={styles.swipeText}>Swipe</Text>
              <AntDesign
                name="swapright"
                size={20}
                color="rgba(255, 255, 255, 0.7)"
              />
            </View>
          </View>

          {/* Bottom section with details */}
          <View style={styles.bottomSection}>
            <View style={styles.detailsContainer}>
              <View style={styles.detailRow}>
                <MaterialIcons name="room" size={22} color="white" />
                <Text style={styles.detailText}>{item.room_name}</Text>
              </View>

              <View style={styles.detailRow}>
                <MaterialIcons name="person" size={22} color="white" />
                <Text style={styles.detailText}>{item.instructor}</Text>
              </View>

              <View style={styles.iconContainer}>{getCardIcon()}</View>
            </View>

            {/* Action buttons */}
            <View style={styles.actionsContainer}>
              <TouchableOpacity style={styles.actionButton}>
                <Ionicons
                  name="checkmark-circle-outline"
                  size={24}
                  color="white"
                />
                <Text style={styles.actionText}>Check In</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.actionButton}>
                <Ionicons name="calendar-outline" size={24} color="white" />
                <Text style={styles.actionText}>Details</Text>
              </TouchableOpacity>
            </View>
          </View>
        </LinearGradient>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    width: width,
    height: "100%",
    overflow: "hidden",
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1001, // Very high z-index to ensure it's above everything
  },
  backgroundImage: {
    width: "100%",
    height: "100%",
  },
  gradient: {
    flex: 1,
    justifyContent: "space-between",
    padding: 20,
    alignItems: "center", // Center content horizontally
    paddingTop: 50, // Padding for status bar only
    paddingBottom: 100, // Extra padding at the bottom for tabs
  },
  topSection: {
    flexDirection: "row",
    alignItems: "flex-start", // Align to top
    marginTop: 20,
    width: "100%", // Take full width
    justifyContent: "flex-end", // Position at right
    paddingRight: 20, // Add some padding
  },
  dateContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 12,
    padding: 12,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 15,
  },
  dayNumber: {
    color: "white",
    fontSize: 28,
    fontWeight: "bold",
  },
  monthName: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  classInfoContainer: {
    flex: 1,
    alignItems: "center", // Center content horizontally
  },
  centerSection: {
    alignItems: "center", // Center content horizontally
    justifyContent: "center", // Center content vertically
    flex: 1, // Take available space
    width: "100%", // Take full width
  },
  className: {
    color: "white",
    fontSize: 42,
    fontWeight: "bold",
    marginBottom: 20,
    textAlign: "center", // Center text
    width: "90%", // Limit width for better readability
    textShadowColor: "rgba(0, 0, 0, 0.75)",
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 5,
  },
  classTime: {
    color: "rgba(255, 255, 255, 0.9)",
    fontSize: 22,
    marginBottom: 10,
    textAlign: "center", // Center text
    textShadowColor: "rgba(0, 0, 0, 0.75)",
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  bottomSection: {
    marginBottom: 30,
    width: "100%", // Take full width
    alignItems: "center", // Center content horizontally
  },
  detailsContainer: {
    marginBottom: 20,
    width: "80%", // Limit width for better readability
    alignItems: "center", // Center content horizontally
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
    justifyContent: "center", // Center content horizontally
  },
  detailText: {
    color: "white",
    fontSize: 20,
    marginLeft: 10,
    textAlign: "center", // Center text
    textShadowColor: "rgba(0, 0, 0, 0.75)",
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  iconContainer: {
    position: "absolute",
    right: 10,
    bottom: 0,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 30,
    padding: 10,
  },
  actionsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    borderTopWidth: 1,
    borderTopColor: "rgba(255, 255, 255, 0.3)",
    paddingTop: 20,
    width: "100%", // Take full width
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 30,
  },
  actionText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  swipeIndicator: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 20,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 15,
  },
  swipeText: {
    color: "rgba(255, 255, 255, 0.7)",
    fontSize: 16,
    marginHorizontal: 10,
    fontWeight: "500",
  },
});
