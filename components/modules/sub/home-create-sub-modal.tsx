import { But<PERSON> } from "~/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogClose,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { Text } from "~/components/ui/text";
import { useICanSub } from "~/modules/sub-management/mutations/useICanSub";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import { useHomeSubOptimisticUpdate } from "~/modules/sub-management/queries/useHomeSubOptimisticUpdate";

export function HomeCreateSubRequestModal({
  isOpen,
  setIsOpen,
  id,
  className,
  requestDate,
}: {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  id: number;
  className?: string;
  requestDate?: string;
}) {
  const { trackEvent, EVENTS } = useAnalytics();
  const { refreshSubRequests } = useHomeSubOptimisticUpdate();

  const handleSuccess = () => {
    setIsOpen(false);

    // Force refresh the sub requests data
    refreshSubRequests();

    // Track sub request acceptance
    trackEvent(EVENTS.ACCEPT_SUB_REQUEST, {
      sub_request_id: id,
      class_name: className,
      request_date: requestDate,
    });
  };

  const { mutate: handleICanSub, isPending } = useICanSub(handleSuccess);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-center">Request to sub</DialogTitle>
          <DialogDescription className="mt-2 font-bold">
            Are you sure you want to request to sub this class?
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex flex-row mt-2">
          <Button
            onPress={() => {
              handleICanSub(id);
            }}
            disabled={isPending}
            label={isPending ? "Sending..." : "Yes, Send Request"}
          />

          <DialogClose asChild>
            <Button
              className="bg-gray-500"
              label="No, Cancel"
              variant={"secondary"}
            />
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
