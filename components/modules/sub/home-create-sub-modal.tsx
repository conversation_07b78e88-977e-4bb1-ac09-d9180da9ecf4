import { But<PERSON> } from "~/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Close,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "~/components/ui/dialog";
import { Text } from "~/components/ui/text";
import { useICanSub } from "~/modules/sub-management/mutations/useICanSub";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import { useHomeSubOptimisticUpdate } from "~/modules/sub-management/queries/useHomeSubOptimisticUpdate";

export function HomeCreateSubRequestModal({
  isOpen,
  setIsOpen,
  id,
  className,
  requestDate,
}: {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  id: number;
  className?: string;
  requestDate?: string;
}) {
  const { trackEvent, EVENTS } = useAnalytics();
  const { refreshSubRequests } = useHomeSubOptimisticUpdate();

  const handleSuccess = () => {
    setIsOpen(false);

    // Force refresh the sub requests data
    refreshSubRequests();

    // Track sub request acceptance
    trackEvent(EVENTS.ACCEPT_SUB_REQUEST, {
      sub_request_id: id,
      class_name: className,
      request_date: requestDate,
    });
  };

  const { mutate: handleICanSub, isPending } = useICanSub(handleSuccess);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent
        className="sm:max-w-[425px]"
        accessibilityViewIsModal={true}
        accessibilityLabel="Sub request confirmation dialog"
      >
        <DialogHeader>
          <DialogTitle
            className="text-center"
            accessibilityRole="header"
            accessibilityLabel="Request to sub"
          >
            Request to sub
          </DialogTitle>
          <DialogDescription
            className="mt-2 font-bold"
            accessibilityLabel={`Are you sure you want to request to sub ${
              className ? `the ${className} class` : "this class"
            }?`}
          >
            Are you sure you want to request to sub this class?
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex flex-row mt-2">
          <Button
            onPress={() => {
              handleICanSub(id);
            }}
            disabled={isPending}
            label={isPending ? "Sending..." : "Yes, Send Request"}
            accessibilityLabel={
              isPending ? "Sending sub request" : "Confirm and send sub request"
            }
            accessibilityRole="button"
            accessibilityHint="Confirms your request to substitute for this class"
          />

          <DialogClose asChild>
            <Button
              className="bg-gray-500"
              label="No, Cancel"
              variant={"secondary"}
              accessibilityLabel="Cancel sub request"
              accessibilityRole="button"
              accessibilityHint="Cancels the sub request and closes this dialog"
            />
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
