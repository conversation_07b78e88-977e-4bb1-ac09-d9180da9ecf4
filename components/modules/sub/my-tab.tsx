import { TouchableOpacity, View } from "react-native";
import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardTitle,
} from "~/components/ui/card";
import { Text } from "~/components/ui/text";

import { obtainDateFrame } from "~/modules/classes/utils";
import { SubRequest } from "./types";
import { format } from "date-fns";

import SimpleLineIcons from "@expo/vector-icons/SimpleLineIcons";

import MaterialIcons from "@expo/vector-icons/MaterialIcons";

import { useState } from "react";

import { CancelSubRequestModal } from "./cance-modal";

import EvilIcons from "@expo/vector-icons/EvilIcons";

const DEFAULT_DATE_FORMAT = "E, MMM d";

const obtainIconAndLabel = ({ isApproved }: { isApproved: boolean }) => {
  if (isApproved) {
    return {
      label: "Sub Found",
      icon: <EvilIcons name="check" size={20} color="white" />,
      color: "#1E2059",
    };
  }

  return {
    label: "Requested",
    icon: <MaterialIcons name="swap-calls" size={20} color="white" />,
  };
};

export function MyRequestTab({
  start_time,
  end_time,
  class_name,
  approved_instructor_first_name,
  approved_instructor_last_name,
  request_date,
  id,
  is_approved,
  gym_name,
}: SubRequest) {
  const formattedDate = format(new Date(request_date), DEFAULT_DATE_FORMAT);

  const [openCancelModal, setCancelModal] = useState(false);

  const { icon, label, color } = obtainIconAndLabel({
    isApproved: Boolean(is_approved),
  });

  return (
    <View className="relative mb-2">
      <Card className="w-full dark:border-white">
        <CardContent className="flex flex-row justify-between items-stretch p-0">
          <View className="flex flex-col gap-0.5 flex-1 p-2">
            <CardDescription className="text-sm">
              {`${formattedDate} . ${obtainDateFrame(start_time, end_time)}`}
            </CardDescription>
            <CardTitle className="pb-0 text-base">{class_name}</CardTitle>
            {Boolean(is_approved) && (
              <View className="flex flex-row items-center">
                <MaterialIcons name="swap-calls" size={14} color="#069CC3" />
                <Text className="pl-1 text-sm">
                  Instructor:{" "}
                  {`${approved_instructor_first_name} ${approved_instructor_last_name}`}
                </Text>
              </View>
            )}
            <CardDescription className="pb-0 text-black dark:text-white text-sm">
              {gym_name}
            </CardDescription>
            {!is_approved && (
              <TouchableOpacity
                onPress={() => setCancelModal(true)}
                className="bg-red-400 flex flex-row items-center justify-center   rounded-md w-20 py-1 mt-0.5"
              >
                <Text className="text-white text-sm">Cancel</Text>
                <SimpleLineIcons
                  className="ml-1"
                  name="close"
                  size={14}
                  color="white"
                />
              </TouchableOpacity>
            )}
          </View>

          <TouchableOpacity
            disabled
            style={{ backgroundColor: color || "#069CC3" }}
            className="w-16 flex items-center justify-center rounded-r-md shadow-sm border-l border-white/10"
          >
            <Text
              style={{ fontSize: 9, fontWeight: 700 }}
              className="text-white whitespace-nowrap mr-1"
              numberOfLines={1}
            >
              {label}
            </Text>
            {React.cloneElement(icon as React.ReactElement, { size: 14 })}
          </TouchableOpacity>
        </CardContent>
      </Card>
      {openCancelModal && (
        <CancelSubRequestModal
          setIsOpen={setCancelModal}
          isOpen={openCancelModal}
          id={id}
        />
      )}
    </View>
  );
}
