interface PreapprovedInstructor {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
}

export interface SubRequest {
  approval_note: string | null;
  approved_at: string | null;
  approved_by_first_name: string | null;
  approved_by_last_name: string | null;
  approved_subbing_instructor_first_name: string | null;
  approved_subbing_instructor_last_name: string | null;
  approved_instructor_first_name: string | null;
  approved_instructor_last_name: string | null;
  class_category_id: number;
  class_id: number;
  class_name: string;
  end_time: string;
  gym_name: string;
  i_can_sub_instructors: {
    id?: number;
    email?: string;
  }[];
  id: number;
  is_approved: number;
  is_cancelled: boolean;
  is_past: boolean;
  message: string;
  notes: string | null;
  original_instructor_id: number;
  preapproved_instructors: PreapprovedInstructor[];
  request_date: string;
  request_time: string;
  requesting_instructor_first_name: string;
  requesting_instructor_last_name: string;
  slot_id: number;
  start_time: string;
  urgency: string;
  sub_request_id?: number;
}

export type SubRequestType = {
  i_can_sub_requests: SubRequest[];
  other_instructor_sub_requests: SubRequest[];
  pending_sub_requests: SubRequest[];
  stats: {
    approved_requests_count: number;
    total_staff_requests_count: number;
    urgent_count: number;
    waiting_for_sub_count: number;
  };
};
