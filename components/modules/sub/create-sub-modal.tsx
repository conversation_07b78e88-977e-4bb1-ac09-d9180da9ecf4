import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { useICanSub } from "~/modules/sub-management/mutations/useICanSub";
import { useAnalytics } from "~/modules/hooks/useAnalytics";

export function CreateSubRequestModal({
  isOpen,
  setIsOpen,
  id,
  className,
  requestDate,
}: {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  id: number;
  className?: string;
  requestDate?: string;
}) {
  const { trackEvent, EVENTS } = useAnalytics();

  const { mutate: handleICanSub, isPending } = useICanSub(() => {
    setIsOpen(false);

    // Track sub request acceptance
    trackEvent(EVENTS.ACCEPT_SUB_REQUEST, {
      sub_request_id: id,
      class_name: className,
      request_date: requestDate,
    });
  });

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-center">Request to sub</DialogTitle>
          <DialogDescription className="mt-2 font-bold">
            Are you sure you want to request to sub this class?
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex flex-row mt-2">
          <Button
            onPress={() => {
              handleICanSub(id);
            }}
            disabled={isPending}
            label={isPending ? "Sending..." : "Yes, Send Request"}
          />

          <DialogClose asChild>
            <Button
              className="bg-gray-500"
              label="No, Cancel"
              variant={"secondary"}
            />
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
