import { View } from "react-native";

import { Text } from "~/components/ui/text";

export function StatChip({ label, count }: { label: string; count?: number }) {
  // Determine text size based on count length
  const countValue = count || 0;
  const countLength = countValue.toString().length;

  // Adjust font size for larger numbers
  const fontSize = countLength > 2 ? 14 : 16;

  return (
    <View className="flex border-2 rounded-lg border-[#F79720] h-[50px] w-full mb-3">
      <View
        collapsable={false}
        className="flex flex-row items-center justify-between h-full px-3"
      >
        <Text
          style={{ fontSize: 13 }}
          className="text-left flex-1 mr-2"
          numberOfLines={2}
        >
          {label}
        </Text>
        <Text
          style={{ fontSize, fontWeight: "900" }}
          className="font-extrabold text-right"
        >
          {countValue}
        </Text>
      </View>
    </View>
  );
}
