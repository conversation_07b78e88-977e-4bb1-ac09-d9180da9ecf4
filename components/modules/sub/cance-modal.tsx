import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { Text } from "~/components/ui/text";
import { useCancelRequest } from "~/modules/sub-management/mutations/useCancelRequest";
import { useAnalytics } from "~/modules/hooks/useAnalytics";

export function CancelSubRequestModal({
  isOpen,
  setIsOpen,
  id,
  className,
  requestDate,
}: {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  id: number;
  className?: string;
  requestDate?: string;
}) {
  const { trackEvent, EVENTS } = useAnalytics();

  const { mutate: handleCancellation, isPending } = useCancelRequest(() => {
    setIsOpen(false);

    // Track sub request cancellation
    trackEvent(EVENTS.CANCEL_SUB_REQUEST, {
      sub_request_id: id,
      class_name: className,
      request_date: requestDate,
    });
  });

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-center">Cancel Sub Request?</DialogTitle>
          <DialogDescription className="text-red-500 mt-2 font-bold">
            Are you sure you want to cancel this sub request? It cannot be
            undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex flex-row mt-2">
          <Button
            onPress={() => handleCancellation(id)}
            disabled={isPending}
            variant={"destructive"}
            label={isPending ? "Cancelling..." : "Yes, Cancel Request"}
          />

          <DialogClose asChild>
            <Button
              label="No, Keep Request"
              variant={"secondary"}
              className="bg-gray-200"
              textClassName="text-gray-800"
            />
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
