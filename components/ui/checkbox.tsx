import * as CheckboxPrimitive from "@rn-primitives/checkbox";
import * as React from "react";
import { Platform } from "react-native";
import { Check } from "~/lib/icons/Check";
import { cn } from "~/lib/utils";
import { Label } from "~/components/ui/label";
import { View } from "react-native";
import { useColorScheme } from "~/lib/useColorScheme";

const Checkbox = React.forwardRef<
  CheckboxPrimitive.RootRef,
  CheckboxPrimitive.RootProps & { accessibilityLabel?: string }
>(({ className, accessibilityLabel, ...props }, ref) => {
  return (
    <CheckboxPrimitive.Root
      ref={ref}
      className={cn(
        "web:peer h-4 w-4 native:h-[20] native:w-[20] shrink-0 rounded-sm native:rounded border border-primary web:ring-offset-background web:focus-visible:outline-none web:focus-visible:ring-2 web:focus-visible:ring-ring web:focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
        props.checked && "bg-primary",
        className
      )}
      accessibilityRole="checkbox"
      accessibilityLabel={accessibilityLabel}
      accessibilityState={{
        checked: !!props.checked,
        disabled: !!props.disabled,
      }}
      {...props}
    >
      <CheckboxPrimitive.Indicator
        className={cn("items-center justify-center h-full w-full")}
      >
        <Check
          size={12}
          strokeWidth={Platform.OS === "web" ? 2.5 : 3.5}
          className="text-primary-foreground"
        />
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  );
});
Checkbox.displayName = CheckboxPrimitive.Root.displayName;

export { Checkbox };

export function CheckBoxBase({
  checked,
  setChecked,
  label,
  accessibilityHint,
  disabled,
}: {
  checked: boolean;
  setChecked: (checked: boolean) => void;
  label: string;
  accessibilityHint?: string;
  disabled?: boolean;
}) {
  const labelId = React.useId();
  const [isChecked, setIsChecked] = React.useState(checked);

  const { isDarkColorScheme } = useColorScheme();

  React.useEffect(() => {
    setIsChecked(checked);
  }, [checked]);

  const handleChange = React.useCallback(
    (newValue: boolean) => {
      if (!disabled) {
        setIsChecked(newValue);
        setChecked(newValue);
      }
    },
    [setChecked, disabled]
  );

  return (
    <View style={{ minWidth: 100 }} accessibilityRole="none">
      <View className="flex-row gap-3 items-center">
        <Checkbox
          aria-labelledby={labelId}
          checked={isChecked}
          onCheckedChange={handleChange}
          accessibilityLabel={label}
          accessibilityHint={accessibilityHint}
          disabled={disabled}
          className={cn("", disabled && "opacity-50")}
        />
        <Label
          nativeID={labelId}
          onPress={() => !disabled && handleChange(!isChecked)}
          className={cn(
            "dark:text-white  flex-shrink-1",
            disabled && "opacity-50"
          )}
          numberOfLines={1}
          accessibilityRole="text"
        >
          {label}
        </Label>
      </View>
    </View>
  );
}
