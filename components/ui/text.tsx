import * as Slot from "@rn-primitives/slot";
import type { SlottableTextProps, TextRef } from "@rn-primitives/types";
import * as React from "react";
import { Text as RNText } from "react-native";
import { cn } from "~/lib/utils";
import { useFontScale } from "~/lib/font-scale-context";

type ExtendedTextProps = SlottableTextProps & {
  accessibilityLabel?: string;
  accessibilityHint?: string;
  isHeader?: boolean;
  allowFontScaling?: boolean; // Override to allow font scaling
  maxFontSizeMultiplier?: number; // Maximum font size multiplier
};

const TextClassContext = React.createContext<string | undefined>(undefined);

/**
 * Enhanced Text component with controlled font scaling
 *
 * By default, this component disables font scaling to ensure consistent UI.
 * You can override this behavior by setting allowFontScaling={true}
 */
const Text = React.forwardRef<TextRef, ExtendedTextProps>(
  (
    {
      className,
      asChild = false,
      isHeader,
      accessibilityLabel,
      accessibilityHint,
      allowFontScaling, // Will be determined by context if not provided
      maxFontSizeMultiplier = 1.0, // Default to no scaling
      ...props
    },
    ref
  ) => {
    const textClass = React.useContext(TextClassContext);
    const Component = asChild ? Slot.Text : RNText;

    // Try to get font scale context, but don't fail if not available
    let fontScaleContext;
    try {
      fontScaleContext = useFontScale();
    } catch {
      fontScaleContext = null;
    }

    // Determine if font scaling should be allowed
    const shouldAllowFontScaling = React.useMemo(() => {
      // If explicitly set, use that value
      if (allowFontScaling !== undefined) {
        return allowFontScaling;
      }

      // If we have context, use its decision
      if (fontScaleContext) {
        return !fontScaleContext.shouldDisableFontScaling;
      }

      // Default to false (disable font scaling)
      return false;
    }, [allowFontScaling, fontScaleContext]);

    // Determine the maximum font size multiplier
    const effectiveMaxMultiplier = React.useMemo(() => {
      if (fontScaleContext) {
        return fontScaleContext.maxFontScale;
      }
      return maxFontSizeMultiplier;
    }, [fontScaleContext, maxFontSizeMultiplier]);

    return (
      <Component
        className={cn(
          "text-base text-foreground web:select-text",
          textClass,
          className
        )}
        ref={ref}
        accessibilityRole={isHeader ? "header" : "text"}
        accessibilityLabel={accessibilityLabel}
        accessibilityHint={accessibilityHint}
        allowFontScaling={shouldAllowFontScaling}
        maxFontSizeMultiplier={effectiveMaxMultiplier}
        {...props}
      />
    );
  }
);
Text.displayName = "Text";

export { Text, TextClassContext };
