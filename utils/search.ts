import { format, parse, parseISO } from "date-fns";
import { KeyOption } from "match-sorter";

// Generic type for any data that can be searched
export type SearchableData = Record<string, any>;

// Static date-based search fields that can be reused
export const getDateBasedSearchFields = <
  T extends { start_time: string; date?: string }
>(): KeyOption<T>[] => [
  // Date-based fields (using date field if available, otherwise start_time)
  (item) => {
    const dateStr = item.date || item.start_time.split(" ")[0];
    return format(parseISO(dateStr), "MMMM"); // Month name
  },
  (item) => {
    const dateStr = item.date || item.start_time.split(" ")[0];
    return format(parseISO(dateStr), "d"); // Day of month
  },
  (item) => {
    const dateStr = item.date || item.start_time.split(" ")[0];
    return format(parseISO(dateStr), "EEEE"); // Full day of week
  },
  (item) => {
    const dateStr = item.date || item.start_time.split(" ")[0];
    return format(parseISO(dateStr), "EEE"); // Short day of week
  },
  // Time-based fields
  (item) => {
    // Extract time part from start_time
    const timePart = item.start_time.split(" ")[1] || item.start_time;
    try {
      // Try to parse as HH:mm:ss
      const parsedTime = parse(timePart, "HH:mm:ss", new Date());
      return format(parsedTime, "h:mm a"); // 12-hour format with am/pm
    } catch (e) {
      // If parsing fails, return the original time
      return timePart;
    }
  },
  (item) => {
    // Extract time part from start_time
    const timePart = item.start_time.split(" ")[1] || item.start_time;
    try {
      // Try to parse as HH:mm:ss
      const parsedTime = parse(timePart, "HH:mm:ss", new Date());
      return format(parsedTime, "h:mma"); // 12-hour format with am/pm (no space)
    } catch (e) {
      // If parsing fails, return the original time
      return timePart;
    }
  },
  (item) => {
    // Extract time part from start_time
    const timePart = item.start_time.split(" ")[1] || item.start_time;
    try {
      // Try to parse as HH:mm:ss
      const parsedTime = parse(timePart, "HH:mm:ss", new Date());
      return format(parsedTime, "HH:mm"); // 24-hour format
    } catch (e) {
      // If parsing fails, return the original time
      return timePart;
    }
  },
];

// Generic function to create search fields combining date fields with custom fields
export const createSearchFields = <T extends { start_time: string }>(
  customFields: (keyof T | ((item: T) => string))[]
): KeyOption<T>[] => {
  return [...getDateBasedSearchFields<T>(), ...customFields] as KeyOption<T>[];
};
