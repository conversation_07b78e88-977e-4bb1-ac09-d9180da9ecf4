import { initializeApp, getApp } from "@react-native-firebase/app";
import {
  getAnalytics,
  logEvent as analyticsLogEvent,
  logScreenView as analyticsLogScreenView,
  setUserId as analyticsSetUserId,
  setUserProperty,
  setAnalyticsCollectionEnabled,
  setConsent,
} from "@react-native-firebase/analytics";
import { isUpaceEmail } from "./utils";
import { getSession } from "~/modules/login/auth-provider";

// Silence deprecation warnings if needed
// Uncomment this line if you want to silence the warnings
// globalThis.RNFB_SILENCE_MODULAR_DEPRECATION_WARNINGS = true;

// Initialize Firebase if it hasn't been initialized yet
export const initializeFirebase = () => {
  try {
    // Try to get the existing app instance
    getApp();
    console.log("Firebase already initialized");
  } catch (error) {
    try {
      // Initialize Firebase if it hasn't been initialized yet
      initializeApp({
        apiKey: "AIzaSyBmPqybHRMogSEcwzGa78wHzWiK-J4sGaM",
        authDomain: "upace-connect-app.firebaseapp.com",
        projectId: "upace-connect-app",
        storageBucket: "upace-connect-app.appspot.com",
        messagingSenderId: "827218014067",
        appId: "1:827218014067:web:5a5dd0efb73da1f9e35f06",
      });
      console.log("Firebase initialized successfully");
    } catch (initError) {
      console.error("Failed to initialize Firebase:", initError);
      // Try again after a delay if initialization fails
      setTimeout(() => {
        console.log("Retrying Firebase initialization...");
        initializeFirebase();
      }, 5000);
      return; // Exit to avoid configuring analytics with uninitialized Firebase
    }
  }

  // Configure analytics with proper consent and error handling
  // Use setTimeout to ensure Firebase has time to fully initialize
  setTimeout(() => {
    configureAnalytics();
  }, 1000);
};

// Configure analytics with proper consent and error handling
const configureAnalytics = async () => {
  try {
    // Check if user is from Upace
    const session = await getSession();
    const isUpaceUser = session?.email ? isUpaceEmail(session.email) : false;

    // Disable analytics collection for Upace users
    const analyticsCollection = !isUpaceUser;
    const app = getApp();
    const analytics = getAnalytics(app);

    // Set analytics collection enabled/disabled
    await setAnalyticsCollectionEnabled(analytics, analyticsCollection);

    // Set proper consent for analytics only for non-Upace users
    // This can help with network issues in some cases
    if (!isUpaceUser) {
      await setConsent(analytics, {
        analytics_storage: true,
        ad_storage: true,
        ad_user_data: true,
        ad_personalization: true,
      });
    }

    if (__DEV__) {
      console.log(
        `Firebase Analytics collection ${
          analyticsCollection ? "enabled" : "disabled"
        }${isUpaceUser ? " (Upace user detected)" : ""}`
      );
    }
  } catch (error) {
    console.error("Error configuring analytics:", error);

    // Try again after a short delay if it's a network error
    if (
      error instanceof Error &&
      (error.message.includes("network") ||
        error.message.includes("cannot parse response") ||
        error.message.includes("upload retries exceeds") ||
        error.message.includes("NSLocalizedDescription"))
    ) {
      console.log("Network error detected in analytics, will retry...");
      setTimeout(() => {
        console.log("Retrying analytics configuration after network error...");
        configureAnalytics();
      }, 10000); // Retry after 10 seconds with longer timeout
    }
  }
};

// Utility function to handle Firebase Analytics operations with retry for network errors
const handleAnalyticsOperation = async <T>(
  operation: () => Promise<T>,
  errorMessage: string,
  successMessage?: string
): Promise<T | undefined> => {
  try {
    const result = await operation();
    if (__DEV__ && successMessage) {
      console.log(successMessage);
    }
    return result;
  } catch (error) {
    // Don't crash the app if analytics fails
    console.error(errorMessage, error);

    // Retry once after a delay if it's a network error
    if (
      error instanceof Error &&
      (error.message.includes("network") ||
        error.message.includes("cannot parse response") ||
        error.message.includes("upload retries exceeds") ||
        error.message.includes("NSLocalizedDescription"))
    ) {
      console.log("Network error in analytics operation, will retry...");
      return new Promise((resolve) => {
        setTimeout(async () => {
          try {
            const result = await operation();
            console.log(`Retry succeeded: ${successMessage}`);
            resolve(result);
          } catch (retryError) {
            console.error(`Retry failed: ${errorMessage}`, retryError);
            resolve(undefined);
          }
        }, 5000); // Retry after 5 seconds with longer timeout
      });
    }

    return undefined;
  }
};

// Check if the current user is from Upace
const isCurrentUserFromUpace = async (): Promise<boolean> => {
  try {
    const session = await getSession();
    return session?.email ? isUpaceEmail(session.email) : false;
  } catch (error) {
    console.error("Error checking if user is from Upace:", error);
    return false;
  }
};

// Log a custom event
export const logEvent = async (
  eventName: string,
  params?: Record<string, any>
) => {
  // Skip tracking for Upace users
  if (await isCurrentUserFromUpace()) {
    if (__DEV__) {
      console.log(`Skipped event logging for Upace user: ${eventName}`);
    }
    return;
  }

  await handleAnalyticsOperation(
    async () => {
      const app = getApp();
      const analytics = getAnalytics(app);
      return analyticsLogEvent(analytics, eventName, params);
    },
    "Error logging event:",
    `Event logged: ${eventName}`
  );
};

// Log screen view
export const logScreenView = async (
  screenName: string,
  screenClass?: string
) => {
  // Skip tracking for Upace users
  if (await isCurrentUserFromUpace()) {
    if (__DEV__) {
      console.log(`Skipped screen view logging for Upace user: ${screenName}`);
    }
    return;
  }

  await handleAnalyticsOperation(
    async () => {
      const app = getApp();
      const analytics = getAnalytics(app);
      return analyticsLogScreenView(analytics, {
        screen_name: screenName,
        screen_class: screenClass || screenName,
      });
    },
    "Error logging screen view:",
    `Screen view logged: ${screenName}`
  );
};

// Log user properties
export const setUserProperties = async (properties: Record<string, string>) => {
  // Skip tracking for Upace users
  if (await isCurrentUserFromUpace()) {
    if (__DEV__) {
      console.log(`Skipped setting user properties for Upace user`);
    }
    return;
  }

  await handleAnalyticsOperation(
    async () => {
      const app = getApp();
      const analytics = getAnalytics(app);
      const entries = Object.entries(properties);
      for (const [key, value] of entries) {
        await setUserProperty(analytics, key, value);
      }
      return true;
    },
    "Error setting user properties:",
    "User properties set"
  );
};

// Set user ID
export const setUserId = async (userId: string | null) => {
  // Skip tracking for Upace users
  if (await isCurrentUserFromUpace()) {
    if (__DEV__) {
      console.log(`Skipped setting user ID for Upace user`);
    }
    return;
  }

  await handleAnalyticsOperation(
    async () => {
      const app = getApp();
      const analytics = getAnalytics(app);
      return analyticsSetUserId(analytics, userId);
    },
    "Error setting user ID:",
    `User ID set: ${userId}`
  );
};

// Common event names
export const EVENTS = {
  // Authentication events
  LOGIN: "login",
  LOGOUT: "logout",

  // Navigation events
  TAB_CHANGE: "tab_change",

  // Class events
  VIEW_CLASS: "view_class",
  CLASS_ACTION: "class_action",
  ADD_RESERVATION: "add_reservation",
  CHECK_IN: "check_in",
  VIEW_OCCUPANCY: "view_occupancy",
  VIEW_WAITLIST: "view_waitlist",

  // Appointment events
  VIEW_APPOINTMENT: "view_appointment",
  CREATE_APPOINTMENT: "create_appointment",
  EDIT_APPOINTMENT: "edit_appointment",
  DELETE_APPOINTMENT: "delete_appointment",
  ASSIGN_MEMBERSHIP: "assign_membership",

  // Calendar events
  ADD_TO_CALENDAR: "add_to_calendar",
  SKIP_ADD_TO_CALENDAR: "skip_add_to_calendar",

  // Sub request events
  VIEW_SUB_REQUEST: "view_sub_request",
  CREATE_SUB_REQUEST: "create_sub_request",
  ACCEPT_SUB_REQUEST: "accept_sub_request",
  CANCEL_SUB_REQUEST: "cancel_sub_request",

  // Search events
  SEARCH: "search",

  // Error events
  ERROR: "error",
  API_ERROR: "api_error",
};
