import React, { createContext, use<PERSON>ontext, ReactNode } from "react";
import { PixelRatio } from "react-native";

interface FontScaleContextType {
  maxFontScale: number;
  systemFontScale: number;
  isLargeFontScale: boolean;
  shouldDisableFontScaling: boolean;
  getControlledFontSize: (baseFontSize: number) => number;
}

const FontScaleContext = createContext<FontScaleContextType | undefined>(
  undefined
);

interface FontScaleProviderProps {
  children: ReactNode;
  maxFontScale?: number;
  disableFontScaling?: boolean; // Force disable all font scaling
}

/**
 * FontScaleProvider - Controls font scaling across the app
 *
 * This provider ensures that no matter how large the user sets their device font size,
 * our app will never exceed the maximum font scale we define.
 */
export const FontScaleProvider: React.FC<FontScaleProviderProps> = ({
  children,
  maxFontScale = 1.0, // Default to 1.0 (no scaling) to force our font sizes
  disableFontScaling = true, // Default to true to disable all font scaling
}) => {
  const systemFontScale = PixelRatio.getFontScale();

  // Determine if the system font scale is considered large
  const isLargeFontScale = systemFontScale > 1.2;

  // Decide whether to disable font scaling entirely
  const shouldDisableFontScaling =
    disableFontScaling || systemFontScale > maxFontScale;

  /**
   * Get controlled font size based on our rules
   * If font scaling is disabled, return the base size
   * Otherwise, apply our maximum scale limit
   */
  const getControlledFontSize = (baseFontSize: number): number => {
    if (shouldDisableFontScaling) {
      return baseFontSize; // Return exact base size, ignore system scaling
    }

    // Apply limited scaling if allowed
    const effectiveScale = Math.min(systemFontScale, maxFontScale);
    return baseFontSize * effectiveScale;
  };

  const value: FontScaleContextType = {
    maxFontScale,
    systemFontScale,
    isLargeFontScale,
    shouldDisableFontScaling,
    getControlledFontSize,
  };

  return (
    <FontScaleContext.Provider value={value}>
      {children}
    </FontScaleContext.Provider>
  );
};

export const useFontScale = (): FontScaleContextType => {
  const context = useContext(FontScaleContext);
  if (!context) {
    throw new Error("useFontScale must be used within a FontScaleProvider");
  }
  return context;
};

/**
 * Static utility function for components that can't access context
 * By default, this disables font scaling to force our font sizes
 */
export const getControlledFontSizeStatic = (
  baseFontSize: number,
  maxScale: number = 1.0, // Default to 1.0 to disable scaling
  disableFontScaling: boolean = true
): number => {
  if (disableFontScaling) {
    return baseFontSize; // Return exact base size
  }

  const systemFontScale = PixelRatio.getFontScale();
  const effectiveScale = Math.min(systemFontScale, maxScale);
  return baseFontSize * effectiveScale;
};
