import { safeLogEvent, safeTrackError } from "./safe-analytics";
import { EVENTS } from "./firebase";

/**
 * Track errors in Firebase Analytics
 * @param error The error object
 * @param context Additional context about where the error occurred
 */
export const trackError = (
  error: Error | unknown,
  context?: {
    source?: string;
    action?: string;
    [key: string]: any;
  }
) => {
  // Use the safe tracking method that won't crash the app
  safeTrackError(error, context);
};

/**
 * Track API errors in Firebase Analytics
 * @param error The API error
 * @param endpoint The API endpoint that failed
 * @param method The HTTP method used
 * @param context Additional context
 */
export const trackApiError = (
  error: any,
  endpoint: string,
  method: string,
  context?: Record<string, any>
) => {
  const statusCode = error.response?.status;
  const errorMessage = error.message || "Unknown API error";

  // Use the safe event logging method that won't crash the app
  safeLogEvent(EVENTS.API_ERROR, {
    endpoint,
    method,
    status_code: statusCode,
    error_message: errorMessage,
    ...context,
  });
};
