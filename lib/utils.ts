import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { BASE_API_URL_CLIENT } from "~/constants/base-url";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const isStaging = () => {
  return BASE_API_URL_CLIENT.includes("upacedev.com");
};

// List of Upace email domains
const UPACE_EMAILS = [
  "@upace.com",
  "@upace.dev",
  "@upace.app",
  "@upaceapp.com",
];

// Check if an email is from Upace
export const isUpaceEmail = (email?: string): boolean => {
  if (!email) return false;
  return UPACE_EMAILS.some((domain) => email.toLowerCase().includes(domain));
};
