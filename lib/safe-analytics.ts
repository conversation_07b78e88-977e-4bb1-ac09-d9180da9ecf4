import { Platform } from "react-native";
import { EVENTS, logEvent as firebaseLogEvent } from "./firebase";
import { isUpaceEmail } from "./utils";
import { getSession } from "~/modules/login/auth-provider";

/**
 * A safe wrapper around Firebase analytics that won't crash the app
 * if Firebase is not initialized or has an error
 */
export const safeLogEvent = (
  eventName: string,
  params?: Record<string, any>
) => {
  // First, log to console for immediate debugging
  if (__DEV__) {
    console.log(`[Analytics Event] ${eventName}`, params);
  }

  // Check if user is from Upace before logging to Firebase
  getSession()
    .then((session) => {
      // Skip tracking for Upace users
      if (session?.email && isUpaceEmail(session.email)) {
        if (__DEV__) {
          console.log(`[Analytics] Skipped event for Upace user: ${eventName}`);
        }
        return;
      }

      // Then try to log to Firebase, but don't wait for it and catch any errors
      try {
        // Use setTimeout to move this off the main thread
        setTimeout(() => {
          firebaseLogEvent(eventName, params).catch((error) => {
            console.error(
              `Failed to log event ${eventName} to Firebase:`,
              error
            );
          });
        }, 0);
      } catch (error) {
        // If even the setTimeout fails, just log to console
        console.error(
          `Error setting up Firebase event logging for ${eventName}:`,
          error
        );
      }
    })
    .catch((error) => {
      console.error("Error checking session for analytics:", error);
    });
};

/**
 * Safely track errors without causing additional crashes
 */
export const safeTrackError = (
  error: Error | unknown,
  context?: {
    source?: string;
    action?: string;
    [key: string]: any;
  }
) => {
  // Extract error details
  const errorMessage = error instanceof Error ? error.message : String(error);
  const errorName = error instanceof Error ? error.name : "Unknown";
  const errorStack = error instanceof Error ? error.stack : undefined;

  // Always log to console first
  console.error(
    `Error in ${context?.source || "unknown"} (${
      context?.action || "unknown action"
    })`,
    {
      message: errorMessage,
      name: errorName,
      stack: errorStack,
      ...context,
    }
  );

  // Then try to log to Firebase, but don't crash if it fails
  safeLogEvent(EVENTS.ERROR, {
    error_message: errorMessage,
    error_name: errorName,
    error_stack: errorStack?.substring(0, 100), // Limit stack trace length
    platform: Platform.OS,
    ...context,
  });
};
