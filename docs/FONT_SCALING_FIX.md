# Font Scaling Fix for Auth Screens

## Problem
When users increase their system font size (accessibility feature), the app layout breaks, especially on login and authentication screens where precise layout is critical.

## Solution
A targeted, minimal approach that only affects auth screens without breaking the rest of the app.

## Implementation

### 1. AuthText Component (`components/ui/auth-text.tsx`)
A specialized text component for auth screens that:
- Detects system font scale using `PixelRatio.getFontScale()`
- Disables font scaling when system scale exceeds the maximum (default 1.2x)
- Sets `maxFontSizeMultiplier` to limit scaling
- Only used on auth screens to prevent layout breaks

```tsx
<AuthText maxFontScale={1.1}>
  WELCOME
</AuthText>
```

### 2. Updated Auth Screens
- `app/(auth)/sign-in.tsx` - Uses AuthText for headings and descriptions
- `app/(auth)/forgot-password.tsx` - Uses AuthText for headings and descriptions  
- `app/(auth)/[email].tsx` - Uses AuthText for headings and descriptions
- `components/modules/login/login-bg.tsx` - Uses AuthText for footer text

### 3. Tab Bar Protection
- `app/(classes)/(tabs)/_layout.tsx` - Set `tabBarAllowFontScaling: false` to prevent tab layout breaks

### 4. Login Card Responsive Adjustments
- `components/modules/login/login-card.tsx` - Adjusts card width and padding based on font scale
- `components/modules/login/login-bg.tsx` - Adjusts logo size and positioning for large font scales

## Key Features

✅ **Targeted approach** - Only affects auth screens, rest of app unchanged
✅ **Prevents layout breaks** - Auth screens maintain layout integrity
✅ **Maintains accessibility** - Still allows some font scaling up to safe limits
✅ **No breaking changes** - Main app components unchanged
✅ **Simple implementation** - Single AuthText component handles the logic

## Configuration

- **Auth screens max font scale**: 1.2x (configurable per component)
- **Tab bar font scaling**: Disabled to maintain layout
- **Main app**: Unchanged, uses default React Native font scaling

## Testing

To test the fix:
1. Go to device Settings > Display > Font Size
2. Set to largest font size
3. Navigate to login screens
4. Verify layouts remain intact and functional
5. Check that text is still readable but doesn't break layouts

## Benefits

1. **Minimal impact** - Only changes auth screens
2. **No app-wide breaking changes** - Tabs, notifications, selects remain unchanged
3. **Easy to maintain** - Simple component-based approach
4. **Configurable** - Can adjust max font scale per component
5. **Backward compatible** - No changes to existing components

This solution provides a surgical fix for the font scaling issue without the complexity and potential breakage of a global solution.
